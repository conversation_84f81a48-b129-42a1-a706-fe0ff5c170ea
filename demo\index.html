<!DOCTYPE html>
<html>
    <head>
        <title>fastboot.js demo</title>
        <script src="../dist/fastboot.mjs" type="module"></script>
        <script src="download.js" type="module"></script>
        <script src="ui.js" type="module"></script>
    </head>

    <body>
        <div>
            <p>Status: <span class="status-field">Not connected</span></p>
            <button class="connect-button">Connect device</button>
        </div>

        <hr />

        <div>
            <form class="command-form">
                <label for="command">Command:</label>
                <input type="text" name="command" class="command-input" />
                <input type="submit" value="Send" />
            </form>
            <p>Result:</p>
            <pre class="result-field"></pre>
        </div>

        <hr />

        <div>
            <form class="boot-form">
                <label for="boot-file">Boot image:</label>
                <input type="file" name="boot-file" class="boot-file" />
                <input type="submit" value="Boot" />
            </form>
        </div>

        <hr />

        <div>
            <form class="flash-form">
                <label for="flash-file">Flash image:</label>
                <input type="file" name="flash-file" class="flash-file" />
                <br />
                <label for="flash-partition">Partition:</label>
                <input
                    type="text"
                    name="flash-partition"
                    class="flash-partition"
                />
                <input type="submit" value="Flash" />
            </form>
        </div>

        <hr />

        <div>
            <p>Status: <span class="factory-status-field"></span></p>
            <progress class="factory-progress-bar" max="1" value="0"></progress>
            <button class="reconnect-button" style="display: none;"><h3>Reconnect device</h3></button>
            <br />
            <form class="factory-form">
                <label for="factory-file"
                    >Flash custom factory images zip:</label
                >
                <br />
                <input type="file" name="factory-file" class="factory-file" />
                <br />
                <input type="submit" value="Flash selected zip" />
            </form>
            <br />
            <span>Flash test zip (only works locally):</span>
            <br />
            <button class="download-zip-button">
                Download taimen factory zip
            </button>
            <br />
            <button class="flash-zip-button">Flash downloaded zip</button>
            <br />
            <pre class="factory-flash-log"></pre>
        </div>
    </body>
</html>
