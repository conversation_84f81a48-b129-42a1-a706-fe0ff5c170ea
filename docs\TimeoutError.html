<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> TimeoutError</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    API Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Classes</h3><ul><li><a href="FastbootDevice.html">FastbootDevice</a></li><li><a href="FastbootError.html">FastbootError</a></li><li><a href="TimeoutError.html">TimeoutError</a></li><li><a href="UsbError.html">UsbError</a></li></ul><h3>Global</h3><ul><li><a href="global.html#setDebugLevel">setDebugLevel</a></li><li><a href="global.html#USER_ACTION_MAP">USER_ACTION_MAP</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Class</p>
                    <h1>TimeoutError</h1>
                </header>
                




<section>

<header>
    
        <h2><span class="attribs"><span class="type-signature"></span></span>TimeoutError<span class="signature">()</span><span class="type-signature"></span></h2>
        
            <div class="class-description">Exception class for operations that exceeded their timeout duration.</div>
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class='vertical-section'>
                <div class="members">
                    <div class="member">
                        <div class=name>
                            <span class="tag">Constructor</span>
                        </div>
                        


    
    <h4 class="name" id="TimeoutError">
        <a class="href-link" href="#TimeoutError">#</a>
        
        <span class="code-name">
            
                new TimeoutError<span class="signature">()</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="common.js.html" class="button">View Source</a>
            <span>
                <a href="common.js.html">common.js</a>, <a href="common.js.html#line85">line 85</a>
            </span>
        </p>
    
</dl>






















                    </div>
                </div>
            </div>
        
    
    </div>
    
    

    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="TimeoutError.html">TimeoutError</a></dt>
            <dd></dd>
        </dl>
    

    

    

    

    

    

    

    
</article>

</section>




            </div>
            
            <footer class="footer">
                <div class="content has-text-centered">
                    <p>Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a></p>
                    <p class="sidebar-created-by">
                        <a href="https://github.com/SoftwareBrothers/better-docs" target="_blank">BetterDocs theme</a> provided with <i class="fas fa-heart"></i> by 
                        <a href="http://softwarebrothers.co" target="_blank">SoftwareBrothers - JavaScript Development Agency</a>
                    </p>
                </div>
            </footer>
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

</body>
</html>