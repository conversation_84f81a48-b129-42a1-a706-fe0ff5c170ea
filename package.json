{"name": "android-fastboot", "version": "1.1.3", "description": "JavaScript implementation of fastboot, using WebUSB", "main": "dist/fastboot.cjs", "repository": "https://github.com/kdrag0n/fastboot.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@zip.js/zip.js": "2.2.27"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.0", "@rollup/plugin-typescript": "^8.2.1", "@types/w3c-web-usb": "^1.0.4", "better-docs": "^2.3.2", "eslint": "^7.18.0", "eslint-config-prettier": "^7.2.0", "jsdoc": "^3.6.6", "prettier": "^2.2.1", "rollup": "^2.38.0", "rollup-plugin-terser": "^7.0.2"}, "scripts": {"doc": "jsdoc -c jsdoc.json", "build": "rollup -c"}, "files": ["dist/fastboot.*"]}