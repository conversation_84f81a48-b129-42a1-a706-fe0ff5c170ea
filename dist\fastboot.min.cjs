"use strict";var e;Object.defineProperty(exports,"__esModule",{value:!0}),function(e){e[e.Silent=0]="Silent",e[e.Debug=1]="Debug",e[e.Verbose=2]="Verbose"}(e||(e={}));let t=e.Silent;function n(...e){t>=1&&console.log(...e)}function i(...e){t>=2&&console.log(...e)}function a(e){return new Promise(((t,n)=>{let i=new FileReader;i.onload=()=>{t(i.result)},i.onerror=()=>{n(i.error)},i.readAsArrayBuffer(e)}))}async function r(e,t,n,i,a){let r=(new Date).getTime(),s=!1;e(t,n,0);let o=(async()=>{let a,o=r+i;do{a=(new Date).getTime(),e(t,n,(a-r)/i),await new Promise(((e,t)=>{window.requestAnimationFrame(e)}))}while(!s&&a<o)})();await Promise.race([o,a]),s=!0,await o,await a,e(t,n,1)}class s extends Error{constructor(e){super(`Timeout of ${e} ms exceeded`),this.name="TimeoutError",this.timeout=e}}class o extends Error{constructor(e){super(e),this.name="ImageError"}}var l;!function(e){e[e.Raw=51905]="Raw",e[e.Fill=51906]="Fill",e[e.Skip=51907]="Skip",e[e.Crc32=51908]="Crc32"}(l||(l={}));class d{constructor(e=""){this.type=e,this.blob=new Blob([],{type:this.type})}append(e){this.blob=new Blob([this.blob,e],{type:this.type})}getBlob(){return this.blob}}function c(e){let t=new DataView(e);if(3978755898!==t.getUint32(0,!0))return null;let n=t.getUint16(4,!0),i=t.getUint16(6,!0);if(1!==n||i<0)throw new o(`Unsupported sparse image version ${n}.${i}`);let a=t.getUint16(8,!0),r=t.getUint16(10,!0);if(28!==a||12!==r)throw new o(`Invalid file header size ${a}, chunk header size ${r}`);let s=t.getUint32(12,!0);if(s%4!=0)throw new o(`Block size ${s} is not a multiple of 4`);return{blockSize:s,blocks:t.getUint32(16,!0),chunks:t.getUint32(20,!0),crc32:t.getUint32(24,!0)}}function f(e){let t=new DataView(e);return{type:t.getUint16(0,!0),blocks:t.getUint32(4,!0),dataBytes:t.getUint32(8,!0)-12,data:null}}function u(e){return e.map((e=>e.blocks)).reduce(((e,t)=>e+t),0)}async function p(e,t){let n=new d,i=new ArrayBuffer(28),r=new DataView(i),s=new Uint8Array(i);r.setUint32(0,3978755898,!0),r.setUint16(4,1,!0),r.setUint16(6,0,!0),r.setUint16(8,28,!0),r.setUint16(10,12,!0),r.setUint32(12,e.blockSize,!0),r.setUint32(16,e.blocks,!0),r.setUint32(20,t.length,!0),r.setUint32(24,0,!0),n.append(new Blob([i]));for(let e of t){i=new ArrayBuffer(12+e.data.size),r=new DataView(i),s=new Uint8Array(i),r.setUint16(0,e.type,!0),r.setUint16(2,0,!0),r.setUint32(4,e.blocks,!0),r.setUint32(8,12+e.data.size,!0);let t=new Uint8Array(await a(e.data));s.set(t,12),n.append(new Blob([i]))}return n.getBlob()}function m(e){return e.map((([e,t])=>new Array(e).fill(t,0,e))).flat()}const _=[0,1,2,3].concat(...m([[2,4],[2,5],[4,6],[4,7],[8,8],[8,9],[16,10],[16,11],[32,12],[32,13],[64,14],[64,15],[2,0],[1,16],[1,17],[2,18],[2,19],[4,20],[4,21],[8,22],[8,23],[16,24],[16,25],[32,26],[32,27],[64,28],[64,29]]));function h(){const e=this;function t(e,t){let n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}e.build_tree=function(n){const i=e.dyn_tree,a=e.stat_desc.static_tree,r=e.stat_desc.elems;let s,o,l,d=-1;for(n.heap_len=0,n.heap_max=573,s=0;s<r;s++)0!==i[2*s]?(n.heap[++n.heap_len]=d=s,n.depth[s]=0):i[2*s+1]=0;for(;n.heap_len<2;)l=n.heap[++n.heap_len]=d<2?++d:0,i[2*l]=1,n.depth[l]=0,n.opt_len--,a&&(n.static_len-=a[2*l+1]);for(e.max_code=d,s=Math.floor(n.heap_len/2);s>=1;s--)n.pqdownheap(i,s);l=r;do{s=n.heap[1],n.heap[1]=n.heap[n.heap_len--],n.pqdownheap(i,1),o=n.heap[1],n.heap[--n.heap_max]=s,n.heap[--n.heap_max]=o,i[2*l]=i[2*s]+i[2*o],n.depth[l]=Math.max(n.depth[s],n.depth[o])+1,i[2*s+1]=i[2*o+1]=l,n.heap[1]=l++,n.pqdownheap(i,1)}while(n.heap_len>=2);n.heap[--n.heap_max]=n.heap[1],function(t){const n=e.dyn_tree,i=e.stat_desc.static_tree,a=e.stat_desc.extra_bits,r=e.stat_desc.extra_base,s=e.stat_desc.max_length;let o,l,d,c,f,u,p=0;for(c=0;c<=15;c++)t.bl_count[c]=0;for(n[2*t.heap[t.heap_max]+1]=0,o=t.heap_max+1;o<573;o++)l=t.heap[o],c=n[2*n[2*l+1]+1]+1,c>s&&(c=s,p++),n[2*l+1]=c,l>e.max_code||(t.bl_count[c]++,f=0,l>=r&&(f=a[l-r]),u=n[2*l],t.opt_len+=u*(c+f),i&&(t.static_len+=u*(i[2*l+1]+f)));if(0!==p){do{for(c=s-1;0===t.bl_count[c];)c--;t.bl_count[c]--,t.bl_count[c+1]+=2,t.bl_count[s]--,p-=2}while(p>0);for(c=s;0!==c;c--)for(l=t.bl_count[c];0!==l;)d=t.heap[--o],d>e.max_code||(n[2*d+1]!=c&&(t.opt_len+=(c-n[2*d+1])*n[2*d],n[2*d+1]=c),l--)}}(n),function(e,n,i){const a=[];let r,s,o,l=0;for(r=1;r<=15;r++)a[r]=l=l+i[r-1]<<1;for(s=0;s<=n;s++)o=e[2*s+1],0!==o&&(e[2*s]=t(a[o]++,o))}(i,e.max_code,n.bl_count)}}function x(e,t,n,i,a){const r=this;r.static_tree=e,r.extra_bits=t,r.extra_base=n,r.elems=i,r.max_length=a}h._length_code=[0,1,2,3,4,5,6,7].concat(...m([[2,8],[2,9],[2,10],[2,11],[4,12],[4,13],[4,14],[4,15],[8,16],[8,17],[8,18],[8,19],[16,20],[16,21],[16,22],[16,23],[32,24],[32,25],[32,26],[31,27],[1,28]])),h.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],h.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],h.d_code=function(e){return e<256?_[e]:_[256+(e>>>7)]},h.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],h.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],h.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],h.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],x.static_ltree=[12,8,140,8,76,8,204,8,44,8,172,8,108,8,236,8,28,8,156,8,92,8,220,8,60,8,188,8,124,8,252,8,2,8,130,8,66,8,194,8,34,8,162,8,98,8,226,8,18,8,146,8,82,8,210,8,50,8,178,8,114,8,242,8,10,8,138,8,74,8,202,8,42,8,170,8,106,8,234,8,26,8,154,8,90,8,218,8,58,8,186,8,122,8,250,8,6,8,134,8,70,8,198,8,38,8,166,8,102,8,230,8,22,8,150,8,86,8,214,8,54,8,182,8,118,8,246,8,14,8,142,8,78,8,206,8,46,8,174,8,110,8,238,8,30,8,158,8,94,8,222,8,62,8,190,8,126,8,254,8,1,8,129,8,65,8,193,8,33,8,161,8,97,8,225,8,17,8,145,8,81,8,209,8,49,8,177,8,113,8,241,8,9,8,137,8,73,8,201,8,41,8,169,8,105,8,233,8,25,8,153,8,89,8,217,8,57,8,185,8,121,8,249,8,5,8,133,8,69,8,197,8,37,8,165,8,101,8,229,8,21,8,149,8,85,8,213,8,53,8,181,8,117,8,245,8,13,8,141,8,77,8,205,8,45,8,173,8,109,8,237,8,29,8,157,8,93,8,221,8,61,8,189,8,125,8,253,8,19,9,275,9,147,9,403,9,83,9,339,9,211,9,467,9,51,9,307,9,179,9,435,9,115,9,371,9,243,9,499,9,11,9,267,9,139,9,395,9,75,9,331,9,203,9,459,9,43,9,299,9,171,9,427,9,107,9,363,9,235,9,491,9,27,9,283,9,155,9,411,9,91,9,347,9,219,9,475,9,59,9,315,9,187,9,443,9,123,9,379,9,251,9,507,9,7,9,263,9,135,9,391,9,71,9,327,9,199,9,455,9,39,9,295,9,167,9,423,9,103,9,359,9,231,9,487,9,23,9,279,9,151,9,407,9,87,9,343,9,215,9,471,9,55,9,311,9,183,9,439,9,119,9,375,9,247,9,503,9,15,9,271,9,143,9,399,9,79,9,335,9,207,9,463,9,47,9,303,9,175,9,431,9,111,9,367,9,239,9,495,9,31,9,287,9,159,9,415,9,95,9,351,9,223,9,479,9,63,9,319,9,191,9,447,9,127,9,383,9,255,9,511,9,0,7,64,7,32,7,96,7,16,7,80,7,48,7,112,7,8,7,72,7,40,7,104,7,24,7,88,7,56,7,120,7,4,7,68,7,36,7,100,7,20,7,84,7,52,7,116,7,3,8,131,8,67,8,195,8,35,8,163,8,99,8,227,8],x.static_dtree=[0,5,16,5,8,5,24,5,4,5,20,5,12,5,28,5,2,5,18,5,10,5,26,5,6,5,22,5,14,5,30,5,1,5,17,5,9,5,25,5,5,5,21,5,13,5,29,5,3,5,19,5,11,5,27,5,7,5,23,5],x.static_l_desc=new x(x.static_ltree,h.extra_lbits,257,286,15),x.static_d_desc=new x(x.static_dtree,h.extra_dbits,0,30,15),x.static_bl_desc=new x(null,h.extra_blbits,0,19,7);function w(e,t,n,i,a){const r=this;r.good_length=e,r.max_lazy=t,r.nice_length=n,r.max_chain=i,r.func=a}const b=[new w(0,0,0,0,0),new w(4,4,8,4,1),new w(4,5,16,8,1),new w(4,6,32,32,1),new w(4,4,16,16,2),new w(8,16,32,32,2),new w(8,16,128,128,2),new w(8,32,128,256,2),new w(32,128,258,1024,2),new w(32,258,258,4096,2)],v=["need dictionary","stream end","","","stream error","data error","","buffer error","",""];function g(e,t,n,i){const a=e[2*t],r=e[2*n];return a<r||a==r&&i[t]<=i[n]}function y(){const e=this;let t,n,i,a,r,s,o,l,d,c,f,u,p,m,_,w,y,k,z,U,A,S,j,E,C,I,F,B,$,q,D,M,L;const R=new h,O=new h,V=new h;let W,T,P,H,K,G,Z,N;function Y(){let t;for(t=0;t<286;t++)D[2*t]=0;for(t=0;t<30;t++)M[2*t]=0;for(t=0;t<19;t++)L[2*t]=0;D[512]=1,e.opt_len=e.static_len=0,P=K=0}function J(e,t){let n,i=-1,a=e[1],r=0,s=7,o=4;0===a&&(s=138,o=3),e[2*(t+1)+1]=65535;for(let l=0;l<=t;l++)n=a,a=e[2*(l+1)+1],++r<s&&n==a||(r<o?L[2*n]+=r:0!==n?(n!=i&&L[2*n]++,L[32]++):r<=10?L[34]++:L[36]++,r=0,i=n,0===a?(s=138,o=3):n==a?(s=6,o=3):(s=7,o=4))}function Q(t){e.pending_buf[e.pending++]=t}function X(e){Q(255&e),Q(e>>>8&255)}function ee(e,t){let n;const i=t;N>16-i?(n=e,Z|=n<<N&65535,X(Z),Z=n>>>16-N,N+=i-16):(Z|=e<<N&65535,N+=i)}function te(e,t){const n=2*e;ee(65535&t[n],65535&t[n+1])}function ne(e,t){let n,i,a=-1,r=e[1],s=0,o=7,l=4;for(0===r&&(o=138,l=3),n=0;n<=t;n++)if(i=r,r=e[2*(n+1)+1],!(++s<o&&i==r)){if(s<l)do{te(i,L)}while(0!=--s);else 0!==i?(i!=a&&(te(i,L),s--),te(16,L),ee(s-3,2)):s<=10?(te(17,L),ee(s-3,3)):(te(18,L),ee(s-11,7));s=0,a=i,0===r?(o=138,l=3):i==r?(o=6,l=3):(o=7,l=4)}}function ie(){16==N?(X(Z),Z=0,N=0):N>=8&&(Q(255&Z),Z>>>=8,N-=8)}function ae(t,n){let i,a,r;if(e.pending_buf[H+2*P]=t>>>8&255,e.pending_buf[H+2*P+1]=255&t,e.pending_buf[W+P]=255&n,P++,0===t?D[2*n]++:(K++,t--,D[2*(h._length_code[n]+256+1)]++,M[2*h.d_code(t)]++),0==(8191&P)&&F>2){for(i=8*P,a=A-y,r=0;r<30;r++)i+=M[2*r]*(5+h.extra_dbits[r]);if(i>>>=3,K<Math.floor(P/2)&&i<Math.floor(a/2))return!0}return P==T-1}function re(t,n){let i,a,r,s,o=0;if(0!==P)do{i=e.pending_buf[H+2*o]<<8&65280|255&e.pending_buf[H+2*o+1],a=255&e.pending_buf[W+o],o++,0===i?te(a,t):(r=h._length_code[a],te(r+256+1,t),s=h.extra_lbits[r],0!==s&&(a-=h.base_length[r],ee(a,s)),i--,r=h.d_code(i),te(r,n),s=h.extra_dbits[r],0!==s&&(i-=h.base_dist[r],ee(i,s)))}while(o<P);te(256,t),G=t[513]}function se(){N>8?X(Z):N>0&&Q(255&Z),Z=0,N=0}function oe(t,n,i){ee(0+(i?1:0),3),function(t,n,i){se(),G=8,i&&(X(n),X(~n)),e.pending_buf.set(l.subarray(t,t+n),e.pending),e.pending+=n}(t,n,!0)}function le(t,n,i){let a,r,s=0;F>0?(R.build_tree(e),O.build_tree(e),s=function(){let t;for(J(D,R.max_code),J(M,O.max_code),V.build_tree(e),t=18;t>=3&&0===L[2*h.bl_order[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(),a=e.opt_len+3+7>>>3,r=e.static_len+3+7>>>3,r<=a&&(a=r)):a=r=n+5,n+4<=a&&-1!=t?oe(t,n,i):r==a?(ee(2+(i?1:0),3),re(x.static_ltree,x.static_dtree)):(ee(4+(i?1:0),3),function(e,t,n){let i;for(ee(e-257,5),ee(t-1,5),ee(n-4,4),i=0;i<n;i++)ee(L[2*h.bl_order[i]+1],3);ne(D,e-1),ne(M,t-1)}(R.max_code+1,O.max_code+1,s+1),re(D,M)),Y(),i&&se()}function de(e){le(y>=0?y:-1,A-y,e),y=A,t.flush_pending()}function ce(){let e,n,i,a;do{if(a=d-j-A,0===a&&0===A&&0===j)a=r;else if(-1==a)a--;else if(A>=r+r-262){l.set(l.subarray(r,r+r),0),S-=r,A-=r,y-=r,e=p,i=e;do{n=65535&f[--i],f[i]=n>=r?n-r:0}while(0!=--e);e=r,i=e;do{n=65535&c[--i],c[i]=n>=r?n-r:0}while(0!=--e);a+=r}if(0===t.avail_in)return;e=t.read_buf(l,A+j,a),j+=e,j>=3&&(u=255&l[A],u=(u<<w^255&l[A+1])&_)}while(j<262&&0!==t.avail_in)}function fe(e){let t,n,i=C,a=A,s=E;const d=A>r-262?A-(r-262):0;let f=q;const u=o,p=A+258;let m=l[a+s-1],_=l[a+s];E>=$&&(i>>=2),f>j&&(f=j);do{if(t=e,l[t+s]==_&&l[t+s-1]==m&&l[t]==l[a]&&l[++t]==l[a+1]){a+=2,t++;do{}while(l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&a<p);if(n=258-(p-a),a=p-258,n>s){if(S=e,s=n,n>=f)break;m=l[a+s-1],_=l[a+s]}}}while((e=65535&c[e&u])>d&&0!=--i);return s<=j?s:j}function ue(t){return t.total_in=t.total_out=0,t.msg=null,e.pending=0,e.pending_out=0,n=113,a=0,R.dyn_tree=D,R.stat_desc=x.static_l_desc,O.dyn_tree=M,O.stat_desc=x.static_d_desc,V.dyn_tree=L,V.stat_desc=x.static_bl_desc,Z=0,N=0,G=8,Y(),function(){d=2*r,f[p-1]=0;for(let e=0;e<p-1;e++)f[e]=0;I=b[F].max_lazy,$=b[F].good_length,q=b[F].nice_length,C=b[F].max_chain,A=0,y=0,j=0,k=E=2,U=0,u=0}(),0}e.depth=[],e.bl_count=[],e.heap=[],D=[],M=[],L=[],e.pqdownheap=function(t,n){const i=e.heap,a=i[n];let r=n<<1;for(;r<=e.heap_len&&(r<e.heap_len&&g(t,i[r+1],i[r],e.depth)&&r++,!g(t,a,i[r],e.depth));)i[n]=i[r],n=r,r<<=1;i[n]=a},e.deflateInit=function(t,n,a,d,u,h){return d||(d=8),u||(u=8),h||(h=0),t.msg=null,-1==n&&(n=6),u<1||u>9||8!=d||a<9||a>15||n<0||n>9||h<0||h>2?-2:(t.dstate=e,s=a,r=1<<s,o=r-1,m=u+7,p=1<<m,_=p-1,w=Math.floor((m+3-1)/3),l=new Uint8Array(2*r),c=[],f=[],T=1<<u+6,e.pending_buf=new Uint8Array(4*T),i=4*T,H=Math.floor(T/2),W=3*T,F=n,B=h,ue(t))},e.deflateEnd=function(){return 42!=n&&113!=n&&666!=n?-2:(e.pending_buf=null,f=null,c=null,l=null,e.dstate=null,113==n?-3:0)},e.deflateParams=function(e,t,n){let i=0;return-1==t&&(t=6),t<0||t>9||n<0||n>2?-2:(b[F].func!=b[t].func&&0!==e.total_in&&(i=e.deflate(1)),F!=t&&(F=t,I=b[F].max_lazy,$=b[F].good_length,q=b[F].nice_length,C=b[F].max_chain),B=n,i)},e.deflateSetDictionary=function(e,t,i){let a,s=i,d=0;if(!t||42!=n)return-2;if(s<3)return 0;for(s>r-262&&(s=r-262,d=i-s),l.set(t.subarray(d,d+s),0),A=s,y=s,u=255&l[0],u=(u<<w^255&l[1])&_,a=0;a<=s-3;a++)u=(u<<w^255&l[a+2])&_,c[a&o]=f[u],f[u]=a;return 0},e.deflate=function(d,m){let h,g,C,$,q;if(m>4||m<0)return-2;if(!d.next_out||!d.next_in&&0!==d.avail_in||666==n&&4!=m)return d.msg=v[4],-2;if(0===d.avail_out)return d.msg=v[7],-5;var D;if(t=d,$=a,a=m,42==n&&(g=8+(s-8<<4)<<8,C=(F-1&255)>>1,C>3&&(C=3),g|=C<<6,0!==A&&(g|=32),g+=31-g%31,n=113,Q((D=g)>>8&255),Q(255&D)),0!==e.pending){if(t.flush_pending(),0===t.avail_out)return a=-1,0}else if(0===t.avail_in&&m<=$&&4!=m)return t.msg=v[7],-5;if(666==n&&0!==t.avail_in)return d.msg=v[7],-5;if(0!==t.avail_in||0!==j||0!=m&&666!=n){switch(q=-1,b[F].func){case 0:q=function(e){let n,a=65535;for(a>i-5&&(a=i-5);;){if(j<=1){if(ce(),0===j&&0==e)return 0;if(0===j)break}if(A+=j,j=0,n=y+a,(0===A||A>=n)&&(j=A-n,A=n,de(!1),0===t.avail_out))return 0;if(A-y>=r-262&&(de(!1),0===t.avail_out))return 0}return de(4==e),0===t.avail_out?4==e?2:0:4==e?3:1}(m);break;case 1:q=function(e){let n,i=0;for(;;){if(j<262){if(ce(),j<262&&0==e)return 0;if(0===j)break}if(j>=3&&(u=(u<<w^255&l[A+2])&_,i=65535&f[u],c[A&o]=f[u],f[u]=A),0!==i&&(A-i&65535)<=r-262&&2!=B&&(k=fe(i)),k>=3)if(n=ae(A-S,k-3),j-=k,k<=I&&j>=3){k--;do{A++,u=(u<<w^255&l[A+2])&_,i=65535&f[u],c[A&o]=f[u],f[u]=A}while(0!=--k);A++}else A+=k,k=0,u=255&l[A],u=(u<<w^255&l[A+1])&_;else n=ae(0,255&l[A]),j--,A++;if(n&&(de(!1),0===t.avail_out))return 0}return de(4==e),0===t.avail_out?4==e?2:0:4==e?3:1}(m);break;case 2:q=function(e){let n,i,a=0;for(;;){if(j<262){if(ce(),j<262&&0==e)return 0;if(0===j)break}if(j>=3&&(u=(u<<w^255&l[A+2])&_,a=65535&f[u],c[A&o]=f[u],f[u]=A),E=k,z=S,k=2,0!==a&&E<I&&(A-a&65535)<=r-262&&(2!=B&&(k=fe(a)),k<=5&&(1==B||3==k&&A-S>4096)&&(k=2)),E>=3&&k<=E){i=A+j-3,n=ae(A-1-z,E-3),j-=E-1,E-=2;do{++A<=i&&(u=(u<<w^255&l[A+2])&_,a=65535&f[u],c[A&o]=f[u],f[u]=A)}while(0!=--E);if(U=0,k=2,A++,n&&(de(!1),0===t.avail_out))return 0}else if(0!==U){if(n=ae(0,255&l[A-1]),n&&de(!1),A++,j--,0===t.avail_out)return 0}else U=1,A++,j--}return 0!==U&&(n=ae(0,255&l[A-1]),U=0),de(4==e),0===t.avail_out?4==e?2:0:4==e?3:1}(m)}if(2!=q&&3!=q||(n=666),0==q||2==q)return 0===t.avail_out&&(a=-1),0;if(1==q){if(1==m)ee(2,3),te(256,x.static_ltree),ie(),1+G+10-N<9&&(ee(2,3),te(256,x.static_ltree),ie()),G=7;else if(oe(0,0,!1),3==m)for(h=0;h<p;h++)f[h]=0;if(t.flush_pending(),0===t.avail_out)return a=-1,0}}return 4!=m?0:1}}function k(){const e=this;e.next_in_index=0,e.next_out_index=0,e.avail_in=0,e.total_in=0,e.avail_out=0,e.total_out=0}k.prototype={deflateInit:function(e,t){const n=this;return n.dstate=new y,t||(t=15),n.dstate.deflateInit(n,e,t)},deflate:function(e){const t=this;return t.dstate?t.dstate.deflate(t,e):-2},deflateEnd:function(){const e=this;if(!e.dstate)return-2;const t=e.dstate.deflateEnd();return e.dstate=null,t},deflateParams:function(e,t){const n=this;return n.dstate?n.dstate.deflateParams(n,e,t):-2},deflateSetDictionary:function(e,t){const n=this;return n.dstate?n.dstate.deflateSetDictionary(n,e,t):-2},read_buf:function(e,t,n){const i=this;let a=i.avail_in;return a>n&&(a=n),0===a?0:(i.avail_in-=a,e.set(i.next_in.subarray(i.next_in_index,i.next_in_index+a),t),i.next_in_index+=a,i.total_in+=a,a)},flush_pending:function(){const e=this;let t=e.dstate.pending;t>e.avail_out&&(t=e.avail_out),0!==t&&(e.next_out.set(e.dstate.pending_buf.subarray(e.dstate.pending_out,e.dstate.pending_out+t),e.next_out_index),e.next_out_index+=t,e.dstate.pending_out+=t,e.total_out+=t,e.avail_out-=t,e.dstate.pending-=t,0===e.dstate.pending&&(e.dstate.pending_out=0))}};const z=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],U=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],A=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],S=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],j=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],E=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],C=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function I(){let e,t,n,i,a,r;function s(e,t,s,o,l,d,c,f,u,p,m){let _,h,x,w,b,v,g,y,k,z,U,A,S,j,E;z=0,b=s;do{n[e[t+z]]++,z++,b--}while(0!==b);if(n[0]==s)return c[0]=-1,f[0]=0,0;for(y=f[0],v=1;v<=15&&0===n[v];v++);for(g=v,y<v&&(y=v),b=15;0!==b&&0===n[b];b--);for(x=b,y>b&&(y=b),f[0]=y,j=1<<v;v<b;v++,j<<=1)if((j-=n[v])<0)return-3;if((j-=n[b])<0)return-3;for(n[b]+=j,r[1]=v=0,z=1,S=2;0!=--b;)r[S]=v+=n[z],S++,z++;b=0,z=0;do{0!==(v=e[t+z])&&(m[r[v]++]=b),z++}while(++b<s);for(s=r[x],r[0]=b=0,z=0,w=-1,A=-y,a[0]=0,U=0,E=0;g<=x;g++)for(_=n[g];0!=_--;){for(;g>A+y;){if(w++,A+=y,E=x-A,E=E>y?y:E,(h=1<<(v=g-A))>_+1&&(h-=_+1,S=g,v<E))for(;++v<E&&!((h<<=1)<=n[++S]);)h-=n[S];if(E=1<<v,p[0]+E>1440)return-3;a[w]=U=p[0],p[0]+=E,0!==w?(r[w]=b,i[0]=v,i[1]=y,v=b>>>A-y,i[2]=U-a[w-1]-v,u.set(i,3*(a[w-1]+v))):c[0]=U}for(i[1]=g-A,z>=s?i[0]=192:m[z]<o?(i[0]=m[z]<256?0:96,i[2]=m[z++]):(i[0]=d[m[z]-o]+16+64,i[2]=l[m[z++]-o]),h=1<<g-A,v=b>>>A;v<E;v+=h)u.set(i,3*(U+v));for(v=1<<g-1;0!=(b&v);v>>>=1)b^=v;for(b^=v,k=(1<<A)-1;(b&k)!=r[w];)w--,A-=y,k=(1<<A)-1}return 0!==j&&1!=x?-5:0}function o(s){let o;for(e||(e=[],t=[],n=new Int32Array(16),i=[],a=new Int32Array(15),r=new Int32Array(16)),t.length<s&&(t=[]),o=0;o<s;o++)t[o]=0;for(o=0;o<16;o++)n[o]=0;for(o=0;o<3;o++)i[o]=0;a.set(n.subarray(0,15),0),r.set(n.subarray(0,16),0)}this.inflate_trees_bits=function(n,i,a,r,l){let d;return o(19),e[0]=0,d=s(n,0,19,19,null,null,a,i,r,e,t),-3==d?l.msg="oversubscribed dynamic bit lengths tree":-5!=d&&0!==i[0]||(l.msg="incomplete dynamic bit lengths tree",d=-3),d},this.inflate_trees_dynamic=function(n,i,a,r,l,d,c,f,u){let p;return o(288),e[0]=0,p=s(a,0,n,257,S,j,d,r,f,e,t),0!=p||0===r[0]?(-3==p?u.msg="oversubscribed literal/length tree":-4!=p&&(u.msg="incomplete literal/length tree",p=-3),p):(o(288),p=s(a,n,i,0,E,C,c,l,f,e,t),0!=p||0===l[0]&&n>257?(-3==p?u.msg="oversubscribed distance tree":-5==p?(u.msg="incomplete distance tree",p=-3):-4!=p&&(u.msg="empty distance tree with lengths",p=-3),p):0)}}I.inflate_trees_fixed=function(e,t,n,i){return e[0]=9,t[0]=5,n[0]=U,i[0]=A,0};function F(){const e=this;let t,n,i,a,r=0,s=0,o=0,l=0,d=0,c=0,f=0,u=0,p=0,m=0;function _(e,t,n,i,a,r,s,o){let l,d,c,f,u,p,m,_,h,x,w,b,v,g,y,k;m=o.next_in_index,_=o.avail_in,u=s.bitb,p=s.bitk,h=s.write,x=h<s.read?s.read-h-1:s.end-h,w=z[e],b=z[t];do{for(;p<20;)_--,u|=(255&o.read_byte(m++))<<p,p+=8;if(l=u&w,d=n,c=i,k=3*(c+l),0!==(f=d[k]))for(;;){if(u>>=d[k+1],p-=d[k+1],0!=(16&f)){for(f&=15,v=d[k+2]+(u&z[f]),u>>=f,p-=f;p<15;)_--,u|=(255&o.read_byte(m++))<<p,p+=8;for(l=u&b,d=a,c=r,k=3*(c+l),f=d[k];;){if(u>>=d[k+1],p-=d[k+1],0!=(16&f)){for(f&=15;p<f;)_--,u|=(255&o.read_byte(m++))<<p,p+=8;if(g=d[k+2]+(u&z[f]),u>>=f,p-=f,x-=v,h>=g)y=h-g,h-y>0&&2>h-y?(s.window[h++]=s.window[y++],s.window[h++]=s.window[y++],v-=2):(s.window.set(s.window.subarray(y,y+2),h),h+=2,y+=2,v-=2);else{y=h-g;do{y+=s.end}while(y<0);if(f=s.end-y,v>f){if(v-=f,h-y>0&&f>h-y)do{s.window[h++]=s.window[y++]}while(0!=--f);else s.window.set(s.window.subarray(y,y+f),h),h+=f,y+=f,f=0;y=0}}if(h-y>0&&v>h-y)do{s.window[h++]=s.window[y++]}while(0!=--v);else s.window.set(s.window.subarray(y,y+v),h),h+=v,y+=v,v=0;break}if(0!=(64&f))return o.msg="invalid distance code",v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,-3;l+=d[k+2],l+=u&z[f],k=3*(c+l),f=d[k]}break}if(0!=(64&f))return 0!=(32&f)?(v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,1):(o.msg="invalid literal/length code",v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,-3);if(l+=d[k+2],l+=u&z[f],k=3*(c+l),0===(f=d[k])){u>>=d[k+1],p-=d[k+1],s.window[h++]=d[k+2],x--;break}}else u>>=d[k+1],p-=d[k+1],s.window[h++]=d[k+2],x--}while(x>=258&&_>=10);return v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,0}e.init=function(e,r,s,o,l,d){t=0,f=e,u=r,i=s,p=o,a=l,m=d,n=null},e.proc=function(e,h,x){let w,b,v,g,y,k,U,A=0,S=0,j=0;for(j=h.next_in_index,g=h.avail_in,A=e.bitb,S=e.bitk,y=e.write,k=y<e.read?e.read-y-1:e.end-y;;)switch(t){case 0:if(k>=258&&g>=10&&(e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,x=_(f,u,i,p,a,m,e,h),j=h.next_in_index,g=h.avail_in,A=e.bitb,S=e.bitk,y=e.write,k=y<e.read?e.read-y-1:e.end-y,0!=x)){t=1==x?7:9;break}o=f,n=i,s=p,t=1;case 1:for(w=o;S<w;){if(0===g)return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);x=0,g--,A|=(255&h.read_byte(j++))<<S,S+=8}if(b=3*(s+(A&z[w])),A>>>=n[b+1],S-=n[b+1],v=n[b],0===v){l=n[b+2],t=6;break}if(0!=(16&v)){d=15&v,r=n[b+2],t=2;break}if(0==(64&v)){o=v,s=b/3+n[b+2];break}if(0!=(32&v)){t=7;break}return t=9,h.msg="invalid literal/length code",x=-3,e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);case 2:for(w=d;S<w;){if(0===g)return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);x=0,g--,A|=(255&h.read_byte(j++))<<S,S+=8}r+=A&z[w],A>>=w,S-=w,o=u,n=a,s=m,t=3;case 3:for(w=o;S<w;){if(0===g)return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);x=0,g--,A|=(255&h.read_byte(j++))<<S,S+=8}if(b=3*(s+(A&z[w])),A>>=n[b+1],S-=n[b+1],v=n[b],0!=(16&v)){d=15&v,c=n[b+2],t=4;break}if(0==(64&v)){o=v,s=b/3+n[b+2];break}return t=9,h.msg="invalid distance code",x=-3,e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);case 4:for(w=d;S<w;){if(0===g)return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);x=0,g--,A|=(255&h.read_byte(j++))<<S,S+=8}c+=A&z[w],A>>=w,S-=w,t=5;case 5:for(U=y-c;U<0;)U+=e.end;for(;0!==r;){if(0===k&&(y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k&&(e.write=y,x=e.inflate_flush(h,x),y=e.write,k=y<e.read?e.read-y-1:e.end-y,y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k)))return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);e.window[y++]=e.window[U++],k--,U==e.end&&(U=0),r--}t=0;break;case 6:if(0===k&&(y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k&&(e.write=y,x=e.inflate_flush(h,x),y=e.write,k=y<e.read?e.read-y-1:e.end-y,y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k)))return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);x=0,e.window[y++]=l,k--,t=0;break;case 7:if(S>7&&(S-=8,g++,j--),e.write=y,x=e.inflate_flush(h,x),y=e.write,k=y<e.read?e.read-y-1:e.end-y,e.read!=e.write)return e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);t=8;case 8:return x=1,e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);case 9:return x=-3,e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x);default:return x=-2,e.bitb=A,e.bitk=S,h.avail_in=g,h.total_in+=j-h.next_in_index,h.next_in_index=j,e.write=y,e.inflate_flush(h,x)}},e.free=function(){}}const B=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function $(e,t){const n=this;let i,a=0,r=0,s=0,o=0;const l=[0],d=[0],c=new F;let f=0,u=new Int32Array(4320);const p=new I;n.bitk=0,n.bitb=0,n.window=new Uint8Array(t),n.end=t,n.read=0,n.write=0,n.reset=function(e,t){t&&(t[0]=0),6==a&&c.free(e),a=0,n.bitk=0,n.bitb=0,n.read=n.write=0},n.reset(e,null),n.inflate_flush=function(e,t){let i,a,r;return a=e.next_out_index,r=n.read,i=(r<=n.write?n.write:n.end)-r,i>e.avail_out&&(i=e.avail_out),0!==i&&-5==t&&(t=0),e.avail_out-=i,e.total_out+=i,e.next_out.set(n.window.subarray(r,r+i),a),a+=i,r+=i,r==n.end&&(r=0,n.write==n.end&&(n.write=0),i=n.write-r,i>e.avail_out&&(i=e.avail_out),0!==i&&-5==t&&(t=0),e.avail_out-=i,e.total_out+=i,e.next_out.set(n.window.subarray(r,r+i),a),a+=i,r+=i),e.next_out_index=a,n.read=r,t},n.proc=function(e,t){let m,_,h,x,w,b,v,g;for(x=e.next_in_index,w=e.avail_in,_=n.bitb,h=n.bitk,b=n.write,v=b<n.read?n.read-b-1:n.end-b;;){let y,k,U,A,S,j,E,C;switch(a){case 0:for(;h<3;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}switch(m=7&_,f=1&m,m>>>1){case 0:_>>>=3,h-=3,m=7&h,_>>>=m,h-=m,a=1;break;case 1:y=[],k=[],U=[[]],A=[[]],I.inflate_trees_fixed(y,k,U,A),c.init(y[0],k[0],U[0],0,A[0],0),_>>>=3,h-=3,a=6;break;case 2:_>>>=3,h-=3,a=3;break;case 3:return _>>>=3,h-=3,a=9,e.msg="invalid block type",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t)}break;case 1:for(;h<32;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if((~_>>>16&65535)!=(65535&_))return a=9,e.msg="invalid stored block lengths",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);r=65535&_,_=h=0,a=0!==r?2:0!==f?7:0;break;case 2:if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);if(0===v&&(b==n.end&&0!==n.read&&(b=0,v=b<n.read?n.read-b-1:n.end-b),0===v&&(n.write=b,t=n.inflate_flush(e,t),b=n.write,v=b<n.read?n.read-b-1:n.end-b,b==n.end&&0!==n.read&&(b=0,v=b<n.read?n.read-b-1:n.end-b),0===v)))return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);if(t=0,m=r,m>w&&(m=w),m>v&&(m=v),n.window.set(e.read_buf(x,m),b),x+=m,w-=m,b+=m,v-=m,0!=(r-=m))break;a=0!==f?7:0;break;case 3:for(;h<14;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if(s=m=16383&_,(31&m)>29||(m>>5&31)>29)return a=9,e.msg="too many length or distance symbols",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);if(m=258+(31&m)+(m>>5&31),!i||i.length<m)i=[];else for(g=0;g<m;g++)i[g]=0;_>>>=14,h-=14,o=0,a=4;case 4:for(;o<4+(s>>>10);){for(;h<3;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}i[B[o++]]=7&_,_>>>=3,h-=3}for(;o<19;)i[B[o++]]=0;if(l[0]=7,m=p.inflate_trees_bits(i,l,d,u,e),0!=m)return-3==(t=m)&&(i=null,a=9),n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);o=0,a=5;case 5:for(;m=s,!(o>=258+(31&m)+(m>>5&31));){let r,c;for(m=l[0];h<m;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if(m=u[3*(d[0]+(_&z[m]))+1],c=u[3*(d[0]+(_&z[m]))+2],c<16)_>>>=m,h-=m,i[o++]=c;else{for(g=18==c?7:c-14,r=18==c?11:3;h<m+g;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if(_>>>=m,h-=m,r+=_&z[g],_>>>=g,h-=g,g=o,m=s,g+r>258+(31&m)+(m>>5&31)||16==c&&g<1)return i=null,a=9,e.msg="invalid bit length repeat",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);c=16==c?i[g-1]:0;do{i[g++]=c}while(0!=--r);o=g}}if(d[0]=-1,S=[],j=[],E=[],C=[],S[0]=9,j[0]=6,m=s,m=p.inflate_trees_dynamic(257+(31&m),1+(m>>5&31),i,S,j,E,C,u,e),0!=m)return-3==m&&(i=null,a=9),t=m,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);c.init(S[0],j[0],u,E[0],u,C[0]),a=6;case 6:if(n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,1!=(t=c.proc(n,e,t)))return n.inflate_flush(e,t);if(t=0,c.free(e),x=e.next_in_index,w=e.avail_in,_=n.bitb,h=n.bitk,b=n.write,v=b<n.read?n.read-b-1:n.end-b,0===f){a=0;break}a=7;case 7:if(n.write=b,t=n.inflate_flush(e,t),b=n.write,v=b<n.read?n.read-b-1:n.end-b,n.read!=n.write)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);a=8;case 8:return t=1,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);case 9:return t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);default:return t=-2,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t)}}},n.free=function(e){n.reset(e,null),n.window=null,u=null},n.set_dictionary=function(e,t,i){n.window.set(e.subarray(t,t+i),0),n.read=n.write=i},n.sync_point=function(){return 1==a?1:0}}const q=[0,0,255,255];function D(){const e=this;function t(e){return e&&e.istate?(e.total_in=e.total_out=0,e.msg=null,e.istate.mode=7,e.istate.blocks.reset(e,null),0):-2}e.mode=0,e.method=0,e.was=[0],e.need=0,e.marker=0,e.wbits=0,e.inflateEnd=function(t){return e.blocks&&e.blocks.free(t),e.blocks=null,0},e.inflateInit=function(n,i){return n.msg=null,e.blocks=null,i<8||i>15?(e.inflateEnd(n),-2):(e.wbits=i,n.istate.blocks=new $(n,1<<i),t(n),0)},e.inflate=function(e,t){let n,i;if(!e||!e.istate||!e.next_in)return-2;const a=e.istate;for(t=4==t?-5:0,n=-5;;)switch(a.mode){case 0:if(0===e.avail_in)return n;if(n=t,e.avail_in--,e.total_in++,8!=(15&(a.method=e.read_byte(e.next_in_index++)))){a.mode=13,e.msg="unknown compression method",a.marker=5;break}if(8+(a.method>>4)>a.wbits){a.mode=13,e.msg="invalid window size",a.marker=5;break}a.mode=1;case 1:if(0===e.avail_in)return n;if(n=t,e.avail_in--,e.total_in++,i=255&e.read_byte(e.next_in_index++),((a.method<<8)+i)%31!=0){a.mode=13,e.msg="incorrect header check",a.marker=5;break}if(0==(32&i)){a.mode=7;break}a.mode=2;case 2:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,a.need=(255&e.read_byte(e.next_in_index++))<<24&4278190080,a.mode=3;case 3:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,a.need+=(255&e.read_byte(e.next_in_index++))<<16&16711680,a.mode=4;case 4:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,a.need+=(255&e.read_byte(e.next_in_index++))<<8&65280,a.mode=5;case 5:return 0===e.avail_in?n:(n=t,e.avail_in--,e.total_in++,a.need+=255&e.read_byte(e.next_in_index++),a.mode=6,2);case 6:return a.mode=13,e.msg="need dictionary",a.marker=0,-2;case 7:if(n=a.blocks.proc(e,n),-3==n){a.mode=13,a.marker=0;break}if(0==n&&(n=t),1!=n)return n;n=t,a.blocks.reset(e,a.was),a.mode=12;case 12:return 1;case 13:return-3;default:return-2}},e.inflateSetDictionary=function(e,t,n){let i=0,a=n;if(!e||!e.istate||6!=e.istate.mode)return-2;const r=e.istate;return a>=1<<r.wbits&&(a=(1<<r.wbits)-1,i=n-a),r.blocks.set_dictionary(t,i,a),r.mode=7,0},e.inflateSync=function(e){let n,i,a,r,s;if(!e||!e.istate)return-2;const o=e.istate;if(13!=o.mode&&(o.mode=13,o.marker=0),0===(n=e.avail_in))return-5;for(i=e.next_in_index,a=o.marker;0!==n&&a<4;)e.read_byte(i)==q[a]?a++:a=0!==e.read_byte(i)?0:4-a,i++,n--;return e.total_in+=i-e.next_in_index,e.next_in_index=i,e.avail_in=n,o.marker=a,4!=a?-3:(r=e.total_in,s=e.total_out,t(e),e.total_in=r,e.total_out=s,o.mode=7,0)},e.inflateSyncPoint=function(e){return e&&e.istate&&e.istate.blocks?e.istate.blocks.sync_point():-2}}function M(){}M.prototype={inflateInit:function(e){const t=this;return t.istate=new D,e||(e=15),t.istate.inflateInit(t,e)},inflate:function(e){const t=this;return t.istate?t.istate.inflate(t,e):-2},inflateEnd:function(){const e=this;if(!e.istate)return-2;const t=e.istate.inflateEnd(e);return e.istate=null,t},inflateSync:function(){const e=this;return e.istate?e.istate.inflateSync(e):-2},inflateSetDictionary:function(e,t){const n=this;return n.istate?n.istate.inflateSetDictionary(n,e,t):-2},read_byte:function(e){return this.next_in[e]},read_buf:function(e,t){return this.next_in.subarray(e,e+t)}};const L={chunkSize:524288,maxWorkers:"undefined"!=typeof navigator&&navigator.hardwareConcurrency||2,useWebWorkers:!0,workerScripts:void 0},R=Object.assign({},L);function O(e){if(void 0!==e.chunkSize&&(R.chunkSize=e.chunkSize),void 0!==e.maxWorkers&&(R.maxWorkers=e.maxWorkers),void 0!==e.useWebWorkers&&(R.useWebWorkers=e.useWebWorkers),void 0!==e.Deflate&&(R.Deflate=e.Deflate),void 0!==e.Inflate&&(R.Inflate=e.Inflate),void 0!==e.workerScripts){if(e.workerScripts.deflate){if(!Array.isArray(e.workerScripts.deflate))throw new Error("workerScripts.deflate must be an array");R.workerScripts||(R.workerScripts={}),R.workerScripts.deflate=e.workerScripts.deflate}if(e.workerScripts.inflate){if(!Array.isArray(e.workerScripts.inflate))throw new Error("workerScripts.inflate must be an array");R.workerScripts||(R.workerScripts={}),R.workerScripts.inflate=e.workerScripts.inflate}}}const V={application:{"andrew-inset":"ez",annodex:"anx","atom+xml":"atom","atomcat+xml":"atomcat","atomserv+xml":"atomsrv",bbolin:"lin",cap:["cap","pcap"],"cu-seeme":"cu","davmount+xml":"davmount",dsptype:"tsp",ecmascript:["es","ecma"],futuresplash:"spl",hta:"hta","java-archive":"jar","java-serialized-object":"ser","java-vm":"class",javascript:"js",m3g:"m3g","mac-binhex40":"hqx",mathematica:["nb","ma","mb"],msaccess:"mdb",msword:["doc","dot"],mxf:"mxf",oda:"oda",ogg:"ogx",pdf:"pdf","pgp-keys":"key","pgp-signature":["asc","sig"],"pics-rules":"prf",postscript:["ps","ai","eps","epsi","epsf","eps2","eps3"],rar:"rar","rdf+xml":"rdf","rss+xml":"rss",rtf:"rtf",smil:["smi","smil"],"xhtml+xml":["xhtml","xht"],xml:["xml","xsl","xsd"],"xspf+xml":"xspf",zip:"zip","vnd.android.package-archive":"apk","vnd.cinderella":"cdy","vnd.google-earth.kml+xml":"kml","vnd.google-earth.kmz":"kmz","vnd.mozilla.xul+xml":"xul","vnd.ms-excel":["xls","xlb","xlt","xlm","xla","xlc","xlw"],"vnd.ms-pki.seccat":"cat","vnd.ms-pki.stl":"stl","vnd.ms-powerpoint":["ppt","pps","pot"],"vnd.oasis.opendocument.chart":"odc","vnd.oasis.opendocument.database":"odb","vnd.oasis.opendocument.formula":"odf","vnd.oasis.opendocument.graphics":"odg","vnd.oasis.opendocument.graphics-template":"otg","vnd.oasis.opendocument.image":"odi","vnd.oasis.opendocument.presentation":"odp","vnd.oasis.opendocument.presentation-template":"otp","vnd.oasis.opendocument.spreadsheet":"ods","vnd.oasis.opendocument.spreadsheet-template":"ots","vnd.oasis.opendocument.text":"odt","vnd.oasis.opendocument.text-master":"odm","vnd.oasis.opendocument.text-template":"ott","vnd.oasis.opendocument.text-web":"oth","vnd.openxmlformats-officedocument.spreadsheetml.sheet":"xlsx","vnd.openxmlformats-officedocument.spreadsheetml.template":"xltx","vnd.openxmlformats-officedocument.presentationml.presentation":"pptx","vnd.openxmlformats-officedocument.presentationml.slideshow":"ppsx","vnd.openxmlformats-officedocument.presentationml.template":"potx","vnd.openxmlformats-officedocument.wordprocessingml.document":"docx","vnd.openxmlformats-officedocument.wordprocessingml.template":"dotx","vnd.smaf":"mmf","vnd.stardivision.calc":"sdc","vnd.stardivision.chart":"sds","vnd.stardivision.draw":"sda","vnd.stardivision.impress":"sdd","vnd.stardivision.math":["sdf","smf"],"vnd.stardivision.writer":["sdw","vor"],"vnd.stardivision.writer-global":"sgl","vnd.sun.xml.calc":"sxc","vnd.sun.xml.calc.template":"stc","vnd.sun.xml.draw":"sxd","vnd.sun.xml.draw.template":"std","vnd.sun.xml.impress":"sxi","vnd.sun.xml.impress.template":"sti","vnd.sun.xml.math":"sxm","vnd.sun.xml.writer":"sxw","vnd.sun.xml.writer.global":"sxg","vnd.sun.xml.writer.template":"stw","vnd.symbian.install":["sis","sisx"],"vnd.visio":["vsd","vst","vss","vsw"],"vnd.wap.wbxml":"wbxml","vnd.wap.wmlc":"wmlc","vnd.wap.wmlscriptc":"wmlsc","vnd.wordperfect":"wpd","vnd.wordperfect5.1":"wp5","x-123":"wk","x-7z-compressed":"7z","x-abiword":"abw","x-apple-diskimage":"dmg","x-bcpio":"bcpio","x-bittorrent":"torrent","x-cbr":["cbr","cba","cbt","cb7"],"x-cbz":"cbz","x-cdf":["cdf","cda"],"x-cdlink":"vcd","x-chess-pgn":"pgn","x-cpio":"cpio","x-csh":"csh","x-debian-package":["deb","udeb"],"x-director":["dcr","dir","dxr","cst","cct","cxt","w3d","fgd","swa"],"x-dms":"dms","x-doom":"wad","x-dvi":"dvi","x-httpd-eruby":"rhtml","x-font":"pcf.Z","x-freemind":"mm","x-gnumeric":"gnumeric","x-go-sgf":"sgf","x-graphing-calculator":"gcf","x-gtar":["gtar","taz"],"x-hdf":"hdf","x-httpd-php":["phtml","pht","php"],"x-httpd-php-source":"phps","x-httpd-php3":"php3","x-httpd-php3-preprocessed":"php3p","x-httpd-php4":"php4","x-httpd-php5":"php5","x-ica":"ica","x-info":"info","x-internet-signup":["ins","isp"],"x-iphone":"iii","x-iso9660-image":"iso","x-java-jnlp-file":"jnlp","x-jmol":"jmz","x-killustrator":"kil","x-koan":["skp","skd","skt","skm"],"x-kpresenter":["kpr","kpt"],"x-kword":["kwd","kwt"],"x-latex":"latex","x-lha":"lha","x-lyx":"lyx","x-lzh":"lzh","x-lzx":"lzx","x-maker":["frm","maker","frame","fm","fb","book","fbdoc"],"x-ms-wmd":"wmd","x-ms-wmz":"wmz","x-msdos-program":["com","exe","bat","dll"],"x-msi":"msi","x-netcdf":["nc","cdf"],"x-ns-proxy-autoconfig":["pac","dat"],"x-nwc":"nwc","x-object":"o","x-oz-application":"oza","x-pkcs7-certreqresp":"p7r","x-python-code":["pyc","pyo"],"x-qgis":["qgs","shp","shx"],"x-quicktimeplayer":"qtl","x-redhat-package-manager":"rpm","x-ruby":"rb","x-sh":"sh","x-shar":"shar","x-shockwave-flash":["swf","swfl"],"x-silverlight":"scr","x-stuffit":"sit","x-sv4cpio":"sv4cpio","x-sv4crc":"sv4crc","x-tar":"tar","x-tcl":"tcl","x-tex-gf":"gf","x-tex-pk":"pk","x-texinfo":["texinfo","texi"],"x-trash":["~","%","bak","old","sik"],"x-troff":["t","tr","roff"],"x-troff-man":"man","x-troff-me":"me","x-troff-ms":"ms","x-ustar":"ustar","x-wais-source":"src","x-wingz":"wz","x-x509-ca-cert":["crt","der","cer"],"x-xcf":"xcf","x-xfig":"fig","x-xpinstall":"xpi",applixware:"aw","atomsvc+xml":"atomsvc","ccxml+xml":"ccxml","cdmi-capability":"cdmia","cdmi-container":"cdmic","cdmi-domain":"cdmid","cdmi-object":"cdmio","cdmi-queue":"cdmiq","docbook+xml":"dbk","dssc+der":"dssc","dssc+xml":"xdssc","emma+xml":"emma","epub+zip":"epub",exi:"exi","font-tdpfr":"pfr","gml+xml":"gml","gpx+xml":"gpx",gxf:"gxf",hyperstudio:"stk","inkml+xml":["ink","inkml"],ipfix:"ipfix",json:"json","jsonml+json":"jsonml","lost+xml":"lostxml","mads+xml":"mads",marc:"mrc","marcxml+xml":"mrcx","mathml+xml":"mathml",mbox:"mbox","mediaservercontrol+xml":"mscml","metalink+xml":"metalink","metalink4+xml":"meta4","mets+xml":"mets","mods+xml":"mods",mp21:["m21","mp21"],mp4:"mp4s","oebps-package+xml":"opf","omdoc+xml":"omdoc",onenote:["onetoc","onetoc2","onetmp","onepkg"],oxps:"oxps","patch-ops-error+xml":"xer","pgp-encrypted":"pgp",pkcs10:"p10","pkcs7-mime":["p7m","p7c"],"pkcs7-signature":"p7s",pkcs8:"p8","pkix-attr-cert":"ac","pkix-crl":"crl","pkix-pkipath":"pkipath",pkixcmp:"pki","pls+xml":"pls","prs.cww":"cww","pskc+xml":"pskcxml","reginfo+xml":"rif","relax-ng-compact-syntax":"rnc","resource-lists+xml":"rl","resource-lists-diff+xml":"rld","rls-services+xml":"rs","rpki-ghostbusters":"gbr","rpki-manifest":"mft","rpki-roa":"roa","rsd+xml":"rsd","sbml+xml":"sbml","scvp-cv-request":"scq","scvp-cv-response":"scs","scvp-vp-request":"spq","scvp-vp-response":"spp",sdp:"sdp","set-payment-initiation":"setpay","set-registration-initiation":"setreg","shf+xml":"shf","sparql-query":"rq","sparql-results+xml":"srx",srgs:"gram","srgs+xml":"grxml","sru+xml":"sru","ssdl+xml":"ssdl","ssml+xml":"ssml","tei+xml":["tei","teicorpus"],"thraud+xml":"tfi","timestamped-data":"tsd","vnd.3gpp.pic-bw-large":"plb","vnd.3gpp.pic-bw-small":"psb","vnd.3gpp.pic-bw-var":"pvb","vnd.3gpp2.tcap":"tcap","vnd.3m.post-it-notes":"pwn","vnd.accpac.simply.aso":"aso","vnd.accpac.simply.imp":"imp","vnd.acucobol":"acu","vnd.acucorp":["atc","acutc"],"vnd.adobe.air-application-installer-package+zip":"air","vnd.adobe.formscentral.fcdt":"fcdt","vnd.adobe.fxp":["fxp","fxpl"],"vnd.adobe.xdp+xml":"xdp","vnd.adobe.xfdf":"xfdf","vnd.ahead.space":"ahead","vnd.airzip.filesecure.azf":"azf","vnd.airzip.filesecure.azs":"azs","vnd.amazon.ebook":"azw","vnd.americandynamics.acc":"acc","vnd.amiga.ami":"ami","vnd.anser-web-certificate-issue-initiation":"cii","vnd.anser-web-funds-transfer-initiation":"fti","vnd.antix.game-component":"atx","vnd.apple.installer+xml":"mpkg","vnd.apple.mpegurl":"m3u8","vnd.aristanetworks.swi":"swi","vnd.astraea-software.iota":"iota","vnd.audiograph":"aep","vnd.blueice.multipass":"mpm","vnd.bmi":"bmi","vnd.businessobjects":"rep","vnd.chemdraw+xml":"cdxml","vnd.chipnuts.karaoke-mmd":"mmd","vnd.claymore":"cla","vnd.cloanto.rp9":"rp9","vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"vnd.cluetrust.cartomobile-config":"c11amc","vnd.cluetrust.cartomobile-config-pkg":"c11amz","vnd.commonspace":"csp","vnd.contact.cmsg":"cdbcmsg","vnd.cosmocaller":"cmc","vnd.crick.clicker":"clkx","vnd.crick.clicker.keyboard":"clkk","vnd.crick.clicker.palette":"clkp","vnd.crick.clicker.template":"clkt","vnd.crick.clicker.wordbank":"clkw","vnd.criticaltools.wbs+xml":"wbs","vnd.ctc-posml":"pml","vnd.cups-ppd":"ppd","vnd.curl.car":"car","vnd.curl.pcurl":"pcurl","vnd.dart":"dart","vnd.data-vision.rdz":"rdz","vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"vnd.dece.ttml+xml":["uvt","uvvt"],"vnd.dece.unspecified":["uvx","uvvx"],"vnd.dece.zip":["uvz","uvvz"],"vnd.denovo.fcselayout-link":"fe_launch","vnd.dna":"dna","vnd.dolby.mlp":"mlp","vnd.dpgraph":"dpg","vnd.dreamfactory":"dfac","vnd.ds-keypoint":"kpxx","vnd.dvb.ait":"ait","vnd.dvb.service":"svc","vnd.dynageo":"geo","vnd.ecowin.chart":"mag","vnd.enliven":"nml","vnd.epson.esf":"esf","vnd.epson.msf":"msf","vnd.epson.quickanime":"qam","vnd.epson.salt":"slt","vnd.epson.ssf":"ssf","vnd.eszigno3+xml":["es3","et3"],"vnd.ezpix-album":"ez2","vnd.ezpix-package":"ez3","vnd.fdf":"fdf","vnd.fdsn.mseed":"mseed","vnd.fdsn.seed":["seed","dataless"],"vnd.flographit":"gph","vnd.fluxtime.clip":"ftc","vnd.framemaker":["fm","frame","maker","book"],"vnd.frogans.fnc":"fnc","vnd.frogans.ltf":"ltf","vnd.fsc.weblaunch":"fsc","vnd.fujitsu.oasys":"oas","vnd.fujitsu.oasys2":"oa2","vnd.fujitsu.oasys3":"oa3","vnd.fujitsu.oasysgp":"fg5","vnd.fujitsu.oasysprs":"bh2","vnd.fujixerox.ddd":"ddd","vnd.fujixerox.docuworks":"xdw","vnd.fujixerox.docuworks.binder":"xbd","vnd.fuzzysheet":"fzs","vnd.genomatix.tuxedo":"txd","vnd.geogebra.file":"ggb","vnd.geogebra.tool":"ggt","vnd.geometry-explorer":["gex","gre"],"vnd.geonext":"gxt","vnd.geoplan":"g2w","vnd.geospace":"g3w","vnd.gmx":"gmx","vnd.grafeq":["gqf","gqs"],"vnd.groove-account":"gac","vnd.groove-help":"ghf","vnd.groove-identity-message":"gim","vnd.groove-injector":"grv","vnd.groove-tool-message":"gtm","vnd.groove-tool-template":"tpl","vnd.groove-vcard":"vcg","vnd.hal+xml":"hal","vnd.handheld-entertainment+xml":"zmm","vnd.hbci":"hbci","vnd.hhe.lesson-player":"les","vnd.hp-hpgl":"hpgl","vnd.hp-hpid":"hpid","vnd.hp-hps":"hps","vnd.hp-jlyt":"jlt","vnd.hp-pcl":"pcl","vnd.hp-pclxl":"pclxl","vnd.hydrostatix.sof-data":"sfd-hdstx","vnd.ibm.minipay":"mpy","vnd.ibm.modcap":["afp","listafp","list3820"],"vnd.ibm.rights-management":"irm","vnd.ibm.secure-container":"sc","vnd.iccprofile":["icc","icm"],"vnd.igloader":"igl","vnd.immervision-ivp":"ivp","vnd.immervision-ivu":"ivu","vnd.insors.igm":"igm","vnd.intercon.formnet":["xpw","xpx"],"vnd.intergeo":"i2g","vnd.intu.qbo":"qbo","vnd.intu.qfx":"qfx","vnd.ipunplugged.rcprofile":"rcprofile","vnd.irepository.package+xml":"irp","vnd.is-xpr":"xpr","vnd.isac.fcs":"fcs","vnd.jam":"jam","vnd.jcp.javame.midlet-rms":"rms","vnd.jisp":"jisp","vnd.joost.joda-archive":"joda","vnd.kahootz":["ktz","ktr"],"vnd.kde.karbon":"karbon","vnd.kde.kchart":"chrt","vnd.kde.kformula":"kfo","vnd.kde.kivio":"flw","vnd.kde.kontour":"kon","vnd.kde.kpresenter":["kpr","kpt"],"vnd.kde.kspread":"ksp","vnd.kde.kword":["kwd","kwt"],"vnd.kenameaapp":"htke","vnd.kidspiration":"kia","vnd.kinar":["kne","knp"],"vnd.koan":["skp","skd","skt","skm"],"vnd.kodak-descriptor":"sse","vnd.las.las+xml":"lasxml","vnd.llamagraphics.life-balance.desktop":"lbd","vnd.llamagraphics.life-balance.exchange+xml":"lbe","vnd.lotus-1-2-3":"123","vnd.lotus-approach":"apr","vnd.lotus-freelance":"pre","vnd.lotus-notes":"nsf","vnd.lotus-organizer":"org","vnd.lotus-screencam":"scm","vnd.lotus-wordpro":"lwp","vnd.macports.portpkg":"portpkg","vnd.mcd":"mcd","vnd.medcalcdata":"mc1","vnd.mediastation.cdkey":"cdkey","vnd.mfer":"mwf","vnd.mfmp":"mfm","vnd.micrografx.flo":"flo","vnd.micrografx.igx":"igx","vnd.mif":"mif","vnd.mobius.daf":"daf","vnd.mobius.dis":"dis","vnd.mobius.mbk":"mbk","vnd.mobius.mqy":"mqy","vnd.mobius.msl":"msl","vnd.mobius.plc":"plc","vnd.mobius.txf":"txf","vnd.mophun.application":"mpn","vnd.mophun.certificate":"mpc","vnd.ms-artgalry":"cil","vnd.ms-cab-compressed":"cab","vnd.ms-excel.addin.macroenabled.12":"xlam","vnd.ms-excel.sheet.binary.macroenabled.12":"xlsb","vnd.ms-excel.sheet.macroenabled.12":"xlsm","vnd.ms-excel.template.macroenabled.12":"xltm","vnd.ms-fontobject":"eot","vnd.ms-htmlhelp":"chm","vnd.ms-ims":"ims","vnd.ms-lrm":"lrm","vnd.ms-officetheme":"thmx","vnd.ms-powerpoint.addin.macroenabled.12":"ppam","vnd.ms-powerpoint.presentation.macroenabled.12":"pptm","vnd.ms-powerpoint.slide.macroenabled.12":"sldm","vnd.ms-powerpoint.slideshow.macroenabled.12":"ppsm","vnd.ms-powerpoint.template.macroenabled.12":"potm","vnd.ms-project":["mpp","mpt"],"vnd.ms-word.document.macroenabled.12":"docm","vnd.ms-word.template.macroenabled.12":"dotm","vnd.ms-works":["wps","wks","wcm","wdb"],"vnd.ms-wpl":"wpl","vnd.ms-xpsdocument":"xps","vnd.mseq":"mseq","vnd.musician":"mus","vnd.muvee.style":"msty","vnd.mynfc":"taglet","vnd.neurolanguage.nlu":"nlu","vnd.nitf":["ntf","nitf"],"vnd.noblenet-directory":"nnd","vnd.noblenet-sealer":"nns","vnd.noblenet-web":"nnw","vnd.nokia.n-gage.data":"ngdat","vnd.nokia.n-gage.symbian.install":"n-gage","vnd.nokia.radio-preset":"rpst","vnd.nokia.radio-presets":"rpss","vnd.novadigm.edm":"edm","vnd.novadigm.edx":"edx","vnd.novadigm.ext":"ext","vnd.oasis.opendocument.chart-template":"otc","vnd.oasis.opendocument.formula-template":"odft","vnd.oasis.opendocument.image-template":"oti","vnd.olpc-sugar":"xo","vnd.oma.dd2+xml":"dd2","vnd.openofficeorg.extension":"oxt","vnd.openxmlformats-officedocument.presentationml.slide":"sldx","vnd.osgeo.mapguide.package":"mgp","vnd.osgi.dp":"dp","vnd.osgi.subsystem":"esa","vnd.palm":["pdb","pqa","oprc"],"vnd.pawaafile":"paw","vnd.pg.format":"str","vnd.pg.osasli":"ei6","vnd.picsel":"efif","vnd.pmi.widget":"wg","vnd.pocketlearn":"plf","vnd.powerbuilder6":"pbd","vnd.previewsystems.box":"box","vnd.proteus.magazine":"mgz","vnd.publishare-delta-tree":"qps","vnd.pvi.ptid1":"ptid","vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"vnd.realvnc.bed":"bed","vnd.recordare.musicxml":"mxl","vnd.recordare.musicxml+xml":"musicxml","vnd.rig.cryptonote":"cryptonote","vnd.rn-realmedia":"rm","vnd.rn-realmedia-vbr":"rmvb","vnd.route66.link66+xml":"link66","vnd.sailingtracker.track":"st","vnd.seemail":"see","vnd.sema":"sema","vnd.semd":"semd","vnd.semf":"semf","vnd.shana.informed.formdata":"ifm","vnd.shana.informed.formtemplate":"itp","vnd.shana.informed.interchange":"iif","vnd.shana.informed.package":"ipk","vnd.simtech-mindmapper":["twd","twds"],"vnd.smart.teacher":"teacher","vnd.solent.sdkm+xml":["sdkm","sdkd"],"vnd.spotfire.dxp":"dxp","vnd.spotfire.sfs":"sfs","vnd.stepmania.package":"smzip","vnd.stepmania.stepchart":"sm","vnd.sus-calendar":["sus","susp"],"vnd.svd":"svd","vnd.syncml+xml":"xsm","vnd.syncml.dm+wbxml":"bdm","vnd.syncml.dm+xml":"xdm","vnd.tao.intent-module-archive":"tao","vnd.tcpdump.pcap":["pcap","cap","dmp"],"vnd.tmobile-livetv":"tmo","vnd.trid.tpt":"tpt","vnd.triscape.mxs":"mxs","vnd.trueapp":"tra","vnd.ufdl":["ufd","ufdl"],"vnd.uiq.theme":"utz","vnd.umajin":"umj","vnd.unity":"unityweb","vnd.uoml+xml":"uoml","vnd.vcx":"vcx","vnd.visionary":"vis","vnd.vsf":"vsf","vnd.webturbo":"wtb","vnd.wolfram.player":"nbp","vnd.wqd":"wqd","vnd.wt.stf":"stf","vnd.xara":"xar","vnd.xfdl":"xfdl","vnd.yamaha.hv-dic":"hvd","vnd.yamaha.hv-script":"hvs","vnd.yamaha.hv-voice":"hvp","vnd.yamaha.openscoreformat":"osf","vnd.yamaha.openscoreformat.osfpvg+xml":"osfpvg","vnd.yamaha.smaf-audio":"saf","vnd.yamaha.smaf-phrase":"spf","vnd.yellowriver-custom-menu":"cmp","vnd.zul":["zir","zirz"],"vnd.zzazz.deck+xml":"zaz","voicexml+xml":"vxml",widget:"wgt",winhlp:"hlp","wsdl+xml":"wsdl","wspolicy+xml":"wspolicy","x-ace-compressed":"ace","x-authorware-bin":["aab","x32","u32","vox"],"x-authorware-map":"aam","x-authorware-seg":"aas","x-blorb":["blb","blorb"],"x-bzip":"bz","x-bzip2":["bz2","boz"],"x-cfs-compressed":"cfs","x-chat":"chat","x-conference":"nsc","x-dgc-compressed":"dgc","x-dtbncx+xml":"ncx","x-dtbook+xml":"dtb","x-dtbresource+xml":"res","x-eva":"eva","x-font-bdf":"bdf","x-font-ghostscript":"gsf","x-font-linux-psf":"psf","x-font-otf":"otf","x-font-pcf":"pcf","x-font-snf":"snf","x-font-ttf":["ttf","ttc"],"x-font-type1":["pfa","pfb","pfm","afm"],"x-font-woff":"woff","x-freearc":"arc","x-gca-compressed":"gca","x-glulx":"ulx","x-gramps-xml":"gramps","x-install-instructions":"install","x-lzh-compressed":["lzh","lha"],"x-mie":"mie","x-mobipocket-ebook":["prc","mobi"],"x-ms-application":"application","x-ms-shortcut":"lnk","x-ms-xbap":"xbap","x-msbinder":"obd","x-mscardfile":"crd","x-msclip":"clp","x-msdownload":["exe","dll","com","bat","msi"],"x-msmediaview":["mvb","m13","m14"],"x-msmetafile":["wmf","wmz","emf","emz"],"x-msmoney":"mny","x-mspublisher":"pub","x-msschedule":"scd","x-msterminal":"trm","x-mswrite":"wri","x-nzb":"nzb","x-pkcs12":["p12","pfx"],"x-pkcs7-certificates":["p7b","spc"],"x-research-info-systems":"ris","x-silverlight-app":"xap","x-sql":"sql","x-stuffitx":"sitx","x-subrip":"srt","x-t3vm-image":"t3","x-tads":"gam","x-tex":"tex","x-tex-tfm":"tfm","x-tgif":"obj","x-xliff+xml":"xlf","x-xz":"xz","x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"xaml+xml":"xaml","xcap-diff+xml":"xdf","xenc+xml":"xenc","xml-dtd":"dtd","xop+xml":"xop","xproc+xml":"xpl","xslt+xml":"xslt","xv+xml":["mxml","xhvml","xvml","xvm"],yang:"yang","yin+xml":"yin",envoy:"evy",fractals:"fif","internet-property-stream":"acx",olescript:"axs","vnd.ms-outlook":"msg","vnd.ms-pkicertstore":"sst","x-compress":"z","x-compressed":"tgz","x-gzip":"gz","x-perfmon":["pma","pmc","pml","pmr","pmw"],"x-pkcs7-mime":["p7c","p7m"],"ynd.ms-pkipko":"pko"},audio:{amr:"amr","amr-wb":"awb",annodex:"axa",basic:["au","snd"],flac:"flac",midi:["mid","midi","kar","rmi"],mpeg:["mpga","mpega","mp2","mp3","m4a","mp2a","m2a","m3a"],mpegurl:"m3u",ogg:["oga","ogg","spx"],"prs.sid":"sid","x-aiff":["aif","aiff","aifc"],"x-gsm":"gsm","x-ms-wma":"wma","x-ms-wax":"wax","x-pn-realaudio":"ram","x-realaudio":"ra","x-sd2":"sd2","x-wav":"wav",adpcm:"adp",mp4:"mp4a",s3m:"s3m",silk:"sil","vnd.dece.audio":["uva","uvva"],"vnd.digital-winds":"eol","vnd.dra":"dra","vnd.dts":"dts","vnd.dts.hd":"dtshd","vnd.lucent.voice":"lvp","vnd.ms-playready.media.pya":"pya","vnd.nuera.ecelp4800":"ecelp4800","vnd.nuera.ecelp7470":"ecelp7470","vnd.nuera.ecelp9600":"ecelp9600","vnd.rip":"rip",webm:"weba","x-aac":"aac","x-caf":"caf","x-matroska":"mka","x-pn-realaudio-plugin":"rmp",xm:"xm",mid:["mid","rmi"]},chemical:{"x-alchemy":"alc","x-cache":["cac","cache"],"x-cache-csf":"csf","x-cactvs-binary":["cbin","cascii","ctab"],"x-cdx":"cdx","x-chem3d":"c3d","x-cif":"cif","x-cmdf":"cmdf","x-cml":"cml","x-compass":"cpa","x-crossfire":"bsd","x-csml":["csml","csm"],"x-ctx":"ctx","x-cxf":["cxf","cef"],"x-embl-dl-nucleotide":["emb","embl"],"x-gamess-input":["inp","gam","gamin"],"x-gaussian-checkpoint":["fch","fchk"],"x-gaussian-cube":"cub","x-gaussian-input":["gau","gjc","gjf"],"x-gaussian-log":"gal","x-gcg8-sequence":"gcg","x-genbank":"gen","x-hin":"hin","x-isostar":["istr","ist"],"x-jcamp-dx":["jdx","dx"],"x-kinemage":"kin","x-macmolecule":"mcm","x-macromodel-input":["mmd","mmod"],"x-mdl-molfile":"mol","x-mdl-rdfile":"rd","x-mdl-rxnfile":"rxn","x-mdl-sdfile":["sd","sdf"],"x-mdl-tgf":"tgf","x-mmcif":"mcif","x-mol2":"mol2","x-molconn-Z":"b","x-mopac-graph":"gpt","x-mopac-input":["mop","mopcrt","mpc","zmt"],"x-mopac-out":"moo","x-ncbi-asn1":"asn","x-ncbi-asn1-ascii":["prt","ent"],"x-ncbi-asn1-binary":["val","aso"],"x-pdb":["pdb","ent"],"x-rosdal":"ros","x-swissprot":"sw","x-vamas-iso14976":"vms","x-vmd":"vmd","x-xtel":"xtel","x-xyz":"xyz"},image:{gif:"gif",ief:"ief",jpeg:["jpeg","jpg","jpe"],pcx:"pcx",png:"png","svg+xml":["svg","svgz"],tiff:["tiff","tif"],"vnd.djvu":["djvu","djv"],"vnd.wap.wbmp":"wbmp","x-canon-cr2":"cr2","x-canon-crw":"crw","x-cmu-raster":"ras","x-coreldraw":"cdr","x-coreldrawpattern":"pat","x-coreldrawtemplate":"cdt","x-corelphotopaint":"cpt","x-epson-erf":"erf","x-icon":"ico","x-jg":"art","x-jng":"jng","x-nikon-nef":"nef","x-olympus-orf":"orf","x-photoshop":"psd","x-portable-anymap":"pnm","x-portable-bitmap":"pbm","x-portable-graymap":"pgm","x-portable-pixmap":"ppm","x-rgb":"rgb","x-xbitmap":"xbm","x-xpixmap":"xpm","x-xwindowdump":"xwd",bmp:"bmp",cgm:"cgm",g3fax:"g3",ktx:"ktx","prs.btif":"btif",sgi:"sgi","vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"vnd.dwg":"dwg","vnd.dxf":"dxf","vnd.fastbidsheet":"fbs","vnd.fpx":"fpx","vnd.fst":"fst","vnd.fujixerox.edmics-mmr":"mmr","vnd.fujixerox.edmics-rlc":"rlc","vnd.ms-modi":"mdi","vnd.ms-photo":"wdp","vnd.net-fpx":"npx","vnd.xiff":"xif",webp:"webp","x-3ds":"3ds","x-cmx":"cmx","x-freehand":["fh","fhc","fh4","fh5","fh7"],"x-pict":["pic","pct"],"x-tga":"tga","cis-cod":"cod",pipeg:"jfif"},message:{rfc822:["eml","mime","mht","mhtml","nws"]},model:{iges:["igs","iges"],mesh:["msh","mesh","silo"],vrml:["wrl","vrml"],"x3d+vrml":["x3dv","x3dvz"],"x3d+xml":["x3d","x3dz"],"x3d+binary":["x3db","x3dbz"],"vnd.collada+xml":"dae","vnd.dwf":"dwf","vnd.gdl":"gdl","vnd.gtw":"gtw","vnd.mts":"mts","vnd.vtu":"vtu"},text:{"cache-manifest":["manifest","appcache"],calendar:["ics","icz","ifb"],css:"css",csv:"csv",h323:"323",html:["html","htm","shtml","stm"],iuls:"uls",mathml:"mml",plain:["txt","text","brf","conf","def","list","log","in","bas"],richtext:"rtx",scriptlet:["sct","wsc"],texmacs:["tm","ts"],"tab-separated-values":"tsv","vnd.sun.j2me.app-descriptor":"jad","vnd.wap.wml":"wml","vnd.wap.wmlscript":"wmls","x-bibtex":"bib","x-boo":"boo","x-c++hdr":["h++","hpp","hxx","hh"],"x-c++src":["c++","cpp","cxx","cc"],"x-component":"htc","x-dsrc":"d","x-diff":["diff","patch"],"x-haskell":"hs","x-java":"java","x-literate-haskell":"lhs","x-moc":"moc","x-pascal":["p","pas"],"x-pcs-gcd":"gcd","x-perl":["pl","pm"],"x-python":"py","x-scala":"scala","x-setext":"etx","x-tcl":["tcl","tk"],"x-tex":["tex","ltx","sty","cls"],"x-vcalendar":"vcs","x-vcard":"vcf",n3:"n3","prs.lines.tag":"dsc",sgml:["sgml","sgm"],troff:["t","tr","roff","man","me","ms"],turtle:"ttl","uri-list":["uri","uris","urls"],vcard:"vcard","vnd.curl":"curl","vnd.curl.dcurl":"dcurl","vnd.curl.scurl":"scurl","vnd.curl.mcurl":"mcurl","vnd.dvb.subtitle":"sub","vnd.fly":"fly","vnd.fmi.flexstor":"flx","vnd.graphviz":"gv","vnd.in3d.3dml":"3dml","vnd.in3d.spot":"spot","x-asm":["s","asm"],"x-c":["c","cc","cxx","cpp","h","hh","dic"],"x-fortran":["f","for","f77","f90"],"x-opml":"opml","x-nfo":"nfo","x-sfv":"sfv","x-uuencode":"uu",webviewhtml:"htt"},video:{avif:".avif","3gpp":"3gp",annodex:"axv",dl:"dl",dv:["dif","dv"],fli:"fli",gl:"gl",mpeg:["mpeg","mpg","mpe","m1v","m2v","mp2","mpa","mpv2"],mp4:["mp4","mp4v","mpg4"],quicktime:["qt","mov"],ogg:"ogv","vnd.mpegurl":["mxu","m4u"],"x-flv":"flv","x-la-asf":["lsf","lsx"],"x-mng":"mng","x-ms-asf":["asf","asx","asr"],"x-ms-wm":"wm","x-ms-wmv":"wmv","x-ms-wmx":"wmx","x-ms-wvx":"wvx","x-msvideo":"avi","x-sgi-movie":"movie","x-matroska":["mpv","mkv","mk3d","mks"],"3gpp2":"3g2",h261:"h261",h263:"h263",h264:"h264",jpeg:"jpgv",jpm:["jpm","jpgm"],mj2:["mj2","mjp2"],"vnd.dece.hd":["uvh","uvvh"],"vnd.dece.mobile":["uvm","uvvm"],"vnd.dece.pd":["uvp","uvvp"],"vnd.dece.sd":["uvs","uvvs"],"vnd.dece.video":["uvv","uvvv"],"vnd.dvb.file":"dvb","vnd.fvt":"fvt","vnd.ms-playready.media.pyv":"pyv","vnd.uvvu.mp4":["uvu","uvvu"],"vnd.vivo":"viv",webm:"webm","x-f4v":"f4v","x-m4v":"m4v","x-ms-vob":"vob","x-smv":"smv"},"x-conference":{"x-cooltalk":"ice"},"x-world":{"x-vrml":["vrm","vrml","wrl","flr","wrz","xaf","xof"]}};(()=>{const e={};for(let t in V)if(V.hasOwnProperty(t))for(let n in V[t])if(V[t].hasOwnProperty(n)){const i=V[t][n];if("string"==typeof i)e[i]=t+"/"+n;else for(let a=0;a<i.length;a++)e[i[a]]=t+"/"+n}})();class W{constructor(){this.size=0}init(){this.initialized=!0}}class T extends W{}class P extends W{writeUint8Array(e){this.size+=e.length}}class H extends P{constructor(e){super(),this.encoding=e,this.blob=new Blob([],{type:"text/plain"})}async writeUint8Array(e){super.writeUint8Array(e),this.blob=new Blob([this.blob,e.buffer],{type:"text/plain"})}getData(){const e=new FileReader;return new Promise(((t,n)=>{e.onload=e=>t(e.target.result),e.onerror=n,e.readAsText(this.blob,this.encoding)}))}}class K extends T{constructor(e){super(),this.blob=e,this.size=e.size}async readUint8Array(e,t){const n=new FileReader;return new Promise(((i,a)=>{n.onload=e=>i(new Uint8Array(e.target.result)),n.onerror=a,n.readAsArrayBuffer(this.blob.slice(e,e+t))}))}}class G extends P{constructor(e){super(),this.offset=0,this.contentType=e,this.blob=new Blob([],{type:e})}async writeUint8Array(e){super.writeUint8Array(e),this.blob=new Blob([this.blob,e.buffer],{type:this.contentType}),this.offset=this.blob.size}getData(){return this.blob}}const Z="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ".split("");const N=[];for(let e=0;e<256;e++){let t=e;for(let e=0;e<8;e++)1&t?t=t>>>1^3988292384:t>>>=1;N[e]=t}class Y{constructor(e){this.crc=e||-1}append(e){let t=0|this.crc;for(let n=0,i=0|e.length;n<i;n++)t=t>>>8^N[255&(t^e[n])];this.crc=t}get(){return~this.crc}}const J={concat(e,t){if(0===e.length||0===t.length)return e.concat(t);const n=e[e.length-1],i=J.getPartial(n);return 32===i?e.concat(t):J._shiftRight(t,i,0|n,e.slice(0,e.length-1))},bitLength(e){const t=e.length;if(0===t)return 0;const n=e[t-1];return 32*(t-1)+J.getPartial(n)},clamp(e,t){if(32*e.length<t)return e;const n=(e=e.slice(0,Math.ceil(t/32))).length;return t&=31,n>0&&t&&(e[n-1]=J.partial(t,e[n-1]&2147483648>>t-1,1)),e},partial:(e,t,n)=>32===e?t:(n?0|t:t<<32-e)+1099511627776*e,getPartial:e=>Math.round(e/1099511627776)||32,_shiftRight(e,t,n,i){for(void 0===i&&(i=[]);t>=32;t-=32)i.push(n),n=0;if(0===t)return i.concat(e);for(let a=0;a<e.length;a++)i.push(n|e[a]>>>t),n=e[a]<<32-t;const a=e.length?e[e.length-1]:0,r=J.getPartial(a);return i.push(J.partial(t+r&31,t+r>32?n:i.pop(),1)),i}},Q={bytes:{fromBits(e){const t=J.bitLength(e)/8,n=new Uint8Array(t);let i;for(let a=0;a<t;a++)0==(3&a)&&(i=e[a/4]),n[a]=i>>>24,i<<=8;return n},toBits(e){const t=[];let n,i=0;for(n=0;n<e.length;n++)i=i<<8|e[n],3==(3&n)&&(t.push(i),i=0);return 3&n&&t.push(J.partial(8*(3&n),i)),t}}},X={sha1:function(e){e?(this._h=e._h.slice(0),this._buffer=e._buffer.slice(0),this._length=e._length):this.reset()}};X.sha1.prototype={blockSize:512,reset:function(){const e=this;return e._h=this._init.slice(0),e._buffer=[],e._length=0,e},update:function(e){const t=this;"string"==typeof e&&(e=Q.utf8String.toBits(e));const n=t._buffer=J.concat(t._buffer,e),i=t._length,a=t._length=i+J.bitLength(e);if(a>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");const r=new Uint32Array(n);let s=0;for(let e=t.blockSize+i-(t.blockSize+i&t.blockSize-1);e<=a;e+=t.blockSize)t._block(r.subarray(16*s,16*(s+1))),s+=1;return n.splice(0,16*s),t},finalize:function(){const e=this;let t=e._buffer;const n=e._h;t=J.concat(t,[J.partial(1,1)]);for(let e=t.length+2;15&e;e++)t.push(0);for(t.push(Math.floor(e._length/4294967296)),t.push(0|e._length);t.length;)e._block(t.splice(0,16));return e.reset(),n},_init:[1732584193,4023233417,2562383102,271733878,3285377520],_key:[1518500249,1859775393,2400959708,3395469782],_f:function(e,t,n,i){return e<=19?t&n|~t&i:e<=39?t^n^i:e<=59?t&n|t&i|n&i:e<=79?t^n^i:void 0},_S:function(e,t){return t<<e|t>>>32-e},_block:function(e){const t=this,n=t._h,i=Array(80);for(let t=0;t<16;t++)i[t]=e[t];let a=n[0],r=n[1],s=n[2],o=n[3],l=n[4];for(let e=0;e<=79;e++){e>=16&&(i[e]=t._S(1,i[e-3]^i[e-8]^i[e-14]^i[e-16]));const n=t._S(5,a)+t._f(e,r,s,o)+l+i[e]+t._key[Math.floor(e/20)]|0;l=o,o=s,s=t._S(30,r),r=a,a=n}n[0]=n[0]+a|0,n[1]=n[1]+r|0,n[2]=n[2]+s|0,n[3]=n[3]+o|0,n[4]=n[4]+l|0}};const ee={aes:class{constructor(e){const t=this;t._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],t._tables[0][0][0]||t._precompute();const n=t._tables[0][4],i=t._tables[1],a=e.length;let r,s,o,l=1;if(4!==a&&6!==a&&8!==a)throw new Error("invalid aes key size");for(t._key=[s=e.slice(0),o=[]],r=a;r<4*a+28;r++){let e=s[r-1];(r%a==0||8===a&&r%a==4)&&(e=n[e>>>24]<<24^n[e>>16&255]<<16^n[e>>8&255]<<8^n[255&e],r%a==0&&(e=e<<8^e>>>24^l<<24,l=l<<1^283*(l>>7))),s[r]=s[r-a]^e}for(let e=0;r;e++,r--){const t=s[3&e?r:r-4];o[e]=r<=4||e<4?t:i[0][n[t>>>24]]^i[1][n[t>>16&255]]^i[2][n[t>>8&255]]^i[3][n[255&t]]}}encrypt(e){return this._crypt(e,0)}decrypt(e){return this._crypt(e,1)}_precompute(){const e=this._tables[0],t=this._tables[1],n=e[4],i=t[4],a=[],r=[];let s,o,l,d;for(let e=0;e<256;e++)r[(a[e]=e<<1^283*(e>>7))^e]=e;for(let c=s=0;!n[c];c^=o||1,s=r[s]||1){let r=s^s<<1^s<<2^s<<3^s<<4;r=r>>8^255&r^99,n[c]=r,i[r]=c,d=a[l=a[o=a[c]]];let f=16843009*d^65537*l^257*o^16843008*c,u=257*a[r]^16843008*r;for(let n=0;n<4;n++)e[n][c]=u=u<<24^u>>>8,t[n][r]=f=f<<24^f>>>8}for(let n=0;n<5;n++)e[n]=e[n].slice(0),t[n]=t[n].slice(0)}_crypt(e,t){if(4!==e.length)throw new Error("invalid aes block size");const n=this._key[t],i=n.length/4-2,a=[0,0,0,0],r=this._tables[t],s=r[0],o=r[1],l=r[2],d=r[3],c=r[4];let f,u,p,m=e[0]^n[0],_=e[t?3:1]^n[1],h=e[2]^n[2],x=e[t?1:3]^n[3],w=4;for(let e=0;e<i;e++)f=s[m>>>24]^o[_>>16&255]^l[h>>8&255]^d[255&x]^n[w],u=s[_>>>24]^o[h>>16&255]^l[x>>8&255]^d[255&m]^n[w+1],p=s[h>>>24]^o[x>>16&255]^l[m>>8&255]^d[255&_]^n[w+2],x=s[x>>>24]^o[m>>16&255]^l[_>>8&255]^d[255&h]^n[w+3],w+=4,m=f,_=u,h=p;for(let e=0;e<4;e++)a[t?3&-e:e]=c[m>>>24]<<24^c[_>>16&255]<<16^c[h>>8&255]<<8^c[255&x]^n[w++],f=m,m=_,_=h,h=x,x=f;return a}}},te={ctrGladman:class{constructor(e,t){this._prf=e,this._initIv=t,this._iv=t}reset(){this._iv=this._initIv}update(e){return this.calculate(this._prf,e,this._iv)}incWord(e){if(255==(e>>24&255)){let t=e>>16&255,n=e>>8&255,i=255&e;255===t?(t=0,255===n?(n=0,255===i?i=0:++i):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=i}else e+=1<<24;return e}incCounter(e){0===(e[0]=this.incWord(e[0]))&&(e[1]=this.incWord(e[1]))}calculate(e,t,n){let i;if(!(i=t.length))return[];const a=J.bitLength(t);for(let a=0;a<i;a+=4){this.incCounter(n);const i=e.encrypt(n);t[a]^=i[0],t[a+1]^=i[1],t[a+2]^=i[2],t[a+3]^=i[3]}return J.clamp(t,a)}}},ne={hmacSha1:class{constructor(e){const t=this,n=t._hash=X.sha1,i=[[],[]],a=n.prototype.blockSize/32;t._baseHash=[new n,new n],e.length>a&&(e=n.hash(e));for(let t=0;t<a;t++)i[0][t]=909522486^e[t],i[1][t]=1549556828^e[t];t._baseHash[0].update(i[0]),t._baseHash[1].update(i[1]),t._resultHash=new n(t._baseHash[0])}reset(){const e=this;e._resultHash=new e._hash(e._baseHash[0]),e._updated=!1}update(e){this._updated=!0,this._resultHash.update(e)}digest(){const e=this,t=e._resultHash.finalize(),n=new e._hash(e._baseHash[1]).update(t).finalize();return e.reset(),n}}},ie={name:"PBKDF2"},ae=Object.assign({hash:{name:"HMAC"}},ie),re=Object.assign({iterations:1e3,hash:{name:"SHA-1"}},ie),se=["deriveBits"],oe=[8,12,16],le=[16,24,32],de=[0,0,0,0],ce=crypto.subtle,fe=Q.bytes,ue=ee.aes,pe=te.ctrGladman,me=ne.hmacSha1;class _e{constructor(e,t,n){Object.assign(this,{password:e,signed:t,strength:n-1,pendingInput:new Uint8Array(0)})}async append(e){const t=this;if(t.password){const n=ve(e,0,oe[t.strength]+2);await async function(e,t,n){await we(e,n,ve(t,0,oe[e.strength]));const i=ve(t,oe[e.strength]),a=e.keys.passwordVerification;if(a[0]!=i[0]||a[1]!=i[1])throw new Error("Invalid pasword")}(t,n,t.password),t.password=null,t.aesCtrGladman=new pe(new ue(t.keys.key),Array.from(de)),t.hmac=new me(t.keys.authentication),e=ve(e,oe[t.strength]+2)}return xe(t,e,new Uint8Array(e.length-10-(e.length-10)%16),0,10,!0)}async flush(){const e=this,t=e.pendingInput,n=ve(t,0,t.length-10),i=ve(t,t.length-10);let a=new Uint8Array(0);if(n.length){const t=fe.toBits(n);e.hmac.update(t);const i=e.aesCtrGladman.update(t);a=fe.fromBits(i)}let r=!0;if(e.signed){const t=ve(fe.fromBits(e.hmac.digest()),0,10);for(let e=0;e<10;e++)t[e]!=i[e]&&(r=!1)}return{valid:r,data:a}}}class he{constructor(e,t){Object.assign(this,{password:e,strength:t-1,pendingInput:new Uint8Array(0)})}async append(e){const t=this;let n=new Uint8Array(0);t.password&&(n=await async function(e,t){const n=crypto.getRandomValues(new Uint8Array(oe[e.strength]));return await we(e,t,n),be(n,e.keys.passwordVerification)}(t,t.password),t.password=null,t.aesCtrGladman=new pe(new ue(t.keys.key),Array.from(de)),t.hmac=new me(t.keys.authentication));const i=new Uint8Array(n.length+e.length-e.length%16);return i.set(n,0),xe(t,e,i,n.length,0)}async flush(){const e=this;let t=new Uint8Array(0);if(e.pendingInput.length){const n=e.aesCtrGladman.update(fe.toBits(e.pendingInput));e.hmac.update(n),t=fe.fromBits(n)}const n=ve(fe.fromBits(e.hmac.digest()),0,10);return{data:be(t,n),signature:n}}}function xe(e,t,n,i,a,r){const s=t.length-a;let o;for(e.pendingInput.length&&(t=be(e.pendingInput,t),n=function(e,t){if(t&&t>e.length){const n=e;(e=new Uint8Array(t)).set(n,0)}return e}(n,s-s%16)),o=0;o<=s-16;o+=16){const a=fe.toBits(ve(t,o,o+16));r&&e.hmac.update(a);const s=e.aesCtrGladman.update(a);r||e.hmac.update(s),n.set(fe.fromBits(s),o+i)}return e.pendingInput=ve(t,o),n}async function we(e,t,n){const i=(new TextEncoder).encode(t),a=await ce.importKey("raw",i,ae,!1,se),r=await ce.deriveBits(Object.assign({salt:n},re),a,8*(2*le[e.strength]+2)),s=new Uint8Array(r);e.keys={key:fe.toBits(ve(s,0,le[e.strength])),authentication:fe.toBits(ve(s,le[e.strength],2*le[e.strength])),passwordVerification:ve(s,2*le[e.strength])}}function be(e,t){let n=e;return e.length+t.length&&(n=new Uint8Array(e.length+t.length),n.set(e,0),n.set(t,e.length)),n}function ve(e,t,n){return e.subarray(t,n)}class ge{constructor(e,t){Object.assign(this,{password:e,passwordVerification:t}),Ue(this,e)}async append(e){const t=this;if(t.password){const n=ke(t,e.subarray(0,12));if(t.password=null,n[11]!=t.passwordVerification)throw new Error("Invalid pasword");e=e.subarray(12)}return ke(t,e)}async flush(){return{valid:!0,data:new Uint8Array(0)}}}class ye{constructor(e,t){Object.assign(this,{password:e,passwordVerification:t}),Ue(this,e)}async append(e){const t=this;let n,i;if(t.password){t.password=null;const a=crypto.getRandomValues(new Uint8Array(12));a[11]=t.passwordVerification,n=new Uint8Array(e.length+a.length),n.set(ze(t,a),0),i=12}else n=new Uint8Array(e.length),i=0;return n.set(ze(t,e),i),n}async flush(){return{data:new Uint8Array(0)}}}function ke(e,t){const n=new Uint8Array(t.length);for(let i=0;i<t.length;i++)n[i]=Se(e)^t[i],Ae(e,n[i]);return n}function ze(e,t){const n=new Uint8Array(t.length);for(let i=0;i<t.length;i++)n[i]=Se(e)^t[i],Ae(e,t[i]);return n}function Ue(e,t){e.keys=[305419896,591751049,878082192],e.crcKey0=new Y(e.keys[0]),e.crcKey2=new Y(e.keys[2]);for(let n=0;n<t.length;n++)Ae(e,t.charCodeAt(n))}function Ae(e,t){e.crcKey0.append([t]),e.keys[0]=~e.crcKey0.get(),e.keys[1]=Ee(e.keys[1]+je(e.keys[0])),e.keys[1]=Ee(Math.imul(e.keys[1],134775813)+1),e.crcKey2.append([e.keys[1]>>>24]),e.keys[2]=~e.crcKey2.get()}function Se(e){const t=2|e.keys[2];return je(Math.imul(t,1^t)>>>8)}function je(e){return 255&e}function Ee(e){return 4294967295&e}class Ce{constructor(e,{signature:t,password:n,signed:i,compressed:a,zipCrypto:r,passwordVerification:s,encryptionStrength:o},{chunkSize:l}){const d=Boolean(n);Object.assign(this,{signature:t,encrypted:d,signed:i,compressed:a,inflate:a&&new e({chunkSize:l}),crc32:i&&new Y,zipCrypto:r,decrypt:d&&r?new ge(n,s):new _e(n,i,o)})}async append(e){const t=this;return t.encrypted&&e.length&&(e=await t.decrypt.append(e)),t.compressed&&e.length&&(e=await t.inflate.append(e)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),e}async flush(){const e=this;let t,n=new Uint8Array(0);if(e.encrypted){const t=await e.decrypt.flush();if(!t.valid)throw new Error("Invalid signature");n=t.data}if((!e.encrypted||e.zipCrypto)&&e.signed){const n=new DataView(new Uint8Array(4).buffer);if(t=e.crc32.get(),n.setUint32(0,t),e.cipher!=n.getUint32(0,!1))throw new Error("Invalid signature")}return e.compressed&&(n=await e.inflate.append(n)||new Uint8Array(0),await e.inflate.flush()),{data:n,signature:t}}}class Ie{constructor(e,{encrypted:t,signed:n,compressed:i,level:a,zipCrypto:r,password:s,passwordVerification:o,encryptionStrength:l},{chunkSize:d}){Object.assign(this,{encrypted:t,signed:n,compressed:i,deflate:i&&new e({level:a||5,chunkSize:d}),crc32:n&&new Y,zipCrypto:r,encrypt:t&&r?new ye(s,o):new he(s,l)})}async append(e){const t=this;let n=e;return t.compressed&&e.length&&(n=await t.deflate.append(e)),t.encrypted&&n.length&&(n=await t.encrypt.append(n)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),n}async flush(){const e=this;let t,n=new Uint8Array(0);if(e.compressed&&(n=await e.deflate.flush()||new Uint8Array(0)),e.encrypted){n=await e.encrypt.append(n);const i=await e.encrypt.flush();t=i.signature;const a=new Uint8Array(n.length+i.data.length);a.set(n,0),a.set(i.data,n.length),n=a}return e.encrypted&&!e.zipCrypto||!e.signed||(t=e.crc32.get()),{data:n,signature:t}}}var Fe=(e,t,n,i,a,r,s)=>(Object.assign(e,{busy:!0,codecConstructor:t,options:Object.assign({},n),scripts:s,webWorker:r,onTaskFinished(){e.busy=!1;a(e)&&e.worker&&e.worker.terminate()}}),r?function(e,t){let n;e.interface||(e.worker=new Worker(new URL(e.scripts[0],"undefined"==typeof document?new(require("url").URL)("file:"+__filename).href:document.currentScript&&document.currentScript.src||new URL("fastboot.min.cjs",document.baseURI).href)),e.worker.addEventListener("message",r,!1),e.interface={append:e=>i({type:"append",data:e}),flush:()=>i({type:"flush"})});return e.interface;async function i(i){if(!n){const n=e.options,i=e.scripts.slice(1);await a({scripts:i,type:"init",options:n,config:{chunkSize:t.chunkSize}})}return a(i)}function a(t){const i=e.worker,a=new Promise(((e,t)=>n={resolve:e,reject:t}));try{if(t.data)try{t.data=t.data.buffer,i.postMessage(t,[t.data])}catch(e){i.postMessage(t)}else i.postMessage(t)}catch(t){n.reject(t),n=null,e.onTaskFinished()}return a}function r(t){const i=t.data;if(n){const t=i.error,a=i.type;if(t){const i=new Error(t.message);i.stack=t.stack,n.reject(i),n=null,e.onTaskFinished()}else if("init"==a||"flush"==a||"append"==a){const t=i.data;"flush"==a?(n.resolve({data:new Uint8Array(t),signature:i.signature}),n=null,e.onTaskFinished()):n.resolve(t&&new Uint8Array(t))}}}}(e,i):function(e,t){const n=function(e,t,n){return t.codecType.startsWith("deflate")?new Ie(e,t,n):t.codecType.startsWith("inflate")?new Ce(e,t,n):void 0}(e.codecConstructor,e.options,t);return{async append(t){try{return await n.append(t)}catch(t){throw e.onTaskFinished(),t}},async flush(){try{return await n.flush()}finally{e.onTaskFinished()}}}}(e,i));let Be=[],$e=[];function qe(e){if(e&&e.aborted)throw new Error("Abort error")}async function De(e,t){return t.length&&await e.writeUint8Array(t),t.length}const Me=["filename","rawFilename","directory","encrypted","compressedSize","uncompressedSize","lastModDate","rawLastModDate","comment","rawComment","signature","extraField","rawExtraField","bitFlag","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","filenameUTF8","commentUTF8","offset","zip64","compressionMethod"];class Le{constructor(e){Me.forEach((t=>this[t]=e[t]))}}const Re="File format is not recognized",Oe=["uncompressedSize","compressedSize","offset"];class Ve{constructor(e,t={}){Object.assign(this,{reader:e,options:t,config:R})}async getEntries(e={}){const t=this,n=t.reader;if(n.initialized||await n.init(),n.size<22)throw new Error(Re);const i=await async function(e,t,n,i,a){const r=new Uint8Array(4);!function(e,t,n){e.setUint32(t,n,!0)}(Xe(r),0,t);const s=i+a;return await o(i)||await o(Math.min(s,n));async function o(t){const a=n-t,s=await et(e,a,t);for(let e=s.length-i;e>=0;e--)if(s[e]==r[0]&&s[e+1]==r[1]&&s[e+2]==r[2]&&s[e+3]==r[3])return{offset:a+e,buffer:s.slice(e,e+i).buffer}}}(n,101010256,n.size,22,1048560);if(!i)throw new Error("End of central directory not found");const a=Xe(i);let r=Je(a,12),s=Je(a,16),o=Ye(a,8),l=0;if(4294967295==s||65535==o){const e=Xe(await et(n,i.offset-20,20));if(117853008!=Je(e,0))throw new Error("End of Zip64 central directory not found");s=Qe(e,8);let t=await et(n,s,56),a=Xe(t);const d=i.offset-20-56;if(101075792!=Je(a,0)&&s!=d){const e=s;s=d,l=s-e,t=await et(n,s,56),a=Xe(t)}if(101075792!=Je(a,0))throw new Error("End of Zip64 central directory locator not found");o=Qe(a,24),r=Je(e,4),s-=Qe(a,40)}if(s<0||s>=n.size)throw new Error(Re);let d=0,c=await et(n,s,n.size-s),f=Xe(c);const u=i.offset-r;if(33639248!=Je(f,d)&&s!=u){const e=s;s=u,l=s-e,c=await et(n,s,n.size-s),f=Xe(c)}if(s<0||s>=n.size)throw new Error(Re);const p=[];for(let i=0;i<o;i++){const i=new We(n,t.config,t.options);if(33639248!=Je(f,d))throw new Error("Central directory header not found");Te(i,f,d+6);const a=Boolean(i.bitFlag.languageEncodingFlag),r=d+46,s=r+i.filenameLength,o=s+i.extraFieldLength;Object.assign(i,{compressedSize:0,uncompressedSize:0,commentLength:Ye(f,d+32),directory:16==(16&Ne(f,d+38)),offset:Je(f,d+42)+l,rawFilename:c.subarray(r,s),filenameUTF8:a,commentUTF8:a,rawExtraField:c.subarray(s,o)});const u=o+i.commentLength;i.rawComment=c.subarray(o,u),i.filename=Ge(i.rawFilename,i.filenameUTF8?"utf-8":Ke(t,e,"filenameEncoding")),i.comment=Ge(i.rawComment,i.commentUTF8?"utf-8":Ke(t,e,"commentEncoding")),!i.directory&&i.filename.endsWith("/")&&(i.directory=!0),Pe(i,i,f,d+6);const m=new Le(i);m.getData=(e,t)=>i.getData(e,t),p.push(m),d=u}return p}async close(){}}class We{constructor(e,t,n){Object.assign(this,{reader:e,config:t,options:n})}async getData(e,t={}){const n=this,{reader:i,offset:a,extraFieldAES:r,compressionMethod:s,config:o,bitFlag:l,signature:d,rawLastModDate:c,compressedSize:f}=n,u=n.localDirectory={};i.initialized||await i.init();const p=await et(i,a,30),m=Xe(p);let _=Ke(n,t,"password");if(_=_&&_.length&&_,r&&99!=r.originalCompressionMethod)throw new Error("Compression method not supported");if(0!=s&&8!=s)throw new Error("Compression method not supported");if(67324752!=Je(m,0))throw new Error("Local file header not found");Te(u,m,4);const h=a+30+u.filenameLength,x=h+u.extraFieldLength;u.rawExtraField=p.subarray(h,x),Pe(n,u,m,4);const w=n.encrypted&&u.encrypted,b=w&&!r;if(w){if(!b&&void 0===r.strength)throw new Error("Encryption method not supported");if(!_)throw new Error("File contains encrypted entry")}const v=await function(e,t,n){const i=!(!t.compressed&&!t.signed&&!t.encrypted)&&(t.useWebWorkers||void 0===t.useWebWorkers&&n.useWebWorkers),a=i&&n.workerScripts?n.workerScripts[t.codecType]:[];if(Be.length<n.maxWorkers){const s={};return Be.push(s),Fe(s,e,t,n,r,i,a)}{const s=Be.find((e=>!e.busy));return s?Fe(s,e,t,n,r,i,a):new Promise((n=>$e.push({resolve:n,codecConstructor:e,options:t,webWorker:i,scripts:a})))}function r(e){const t=!$e.length;if(t)Be=Be.filter((t=>t!=e));else{const[{resolve:t,codecConstructor:i,options:a,webWorker:s,scripts:o}]=$e.splice(0,1);t(Fe(e,i,a,n,r,s,o))}return t}}(o.Inflate,{codecType:"inflate",password:_,zipCrypto:b,encryptionStrength:r&&r.strength,signed:Ke(n,t,"checkSignature"),passwordVerification:b&&(l.dataDescriptor?c>>>8&255:d>>>24&255),signature:d,compressed:0!=s,encrypted:w,useWebWorkers:Ke(n,t,"useWebWorkers")},o);e.initialized||await e.init();const g=Ke(n,t,"signal");return await async function(e,t,n,i,a,r,s){const o=Math.max(r.chunkSize,64);return async function r(l=0,d=0){const c=s.signal;if(l<a){qe(c);const f=await t.readUint8Array(l+i,Math.min(o,a-l)),u=f.length;qe(c);const p=await e.append(f);if(qe(c),d+=await De(n,p),s.onprogress)try{s.onprogress(l+u,a)}catch(e){}return r(l+o,d)}{const t=await e.flush();return d+=await De(n,t.data),{signature:t.signature,length:d}}}()}(v,i,e,x,f,o,{onprogress:t.onprogress,signal:g}),e.getData()}}function Te(e,t,n){const i=e.rawBitFlag=Ye(t,n+2),a=1==(1&i);Object.assign(e,{encrypted:a,version:Ye(t,n),bitFlag:{level:(6&i)>>1,dataDescriptor:8==(8&i),languageEncodingFlag:2048==(2048&i)},rawLastModDate:Je(t,n+6),lastModDate:Ze(e.rawLastModDate),filenameLength:Ye(t,n+22),extraFieldLength:Ye(t,n+24)})}function Pe(e,t,n,i){const a=t.rawExtraField,r=t.extraField=new Map,s=Xe(new Uint8Array(a));let o=0;try{for(;o<a.length;){const e=Ye(s,o),t=Ye(s,o+2);r.set(e,{type:e,data:a.slice(o+4,o+4+t)}),o+=4+t}}catch(e){}const l=Ye(n,i+4);t.signature=Je(n,i+10),t.uncompressedSize=Je(n,i+18),t.compressedSize=Je(n,i+14);const d=t.extraFieldZip64=r.get(1);d&&function(e,t){t.zip64=!0;const n=Xe(e.data);e.values=[];for(let t=0;t<Math.floor(e.data.length/8);t++)e.values.push(Qe(n,0+8*t));const i=Oe.filter((e=>4294967295==t[e]));for(let t=0;t<i.length;t++)e[i[t]]=e.values[t];Oe.forEach((n=>{if(4294967295==t[n]){if(!e||void 0===e[n])throw new Error("Zip64 extra field not found");t[n]=e[n]}}))}(d,t);const c=t.extraFieldUnicodePath=r.get(28789);c&&He(c,"filename","rawFilename",t,e);const f=t.extraFieldUnicodeComment=r.get(25461);f&&He(f,"comment","rawComment",t,e);const u=t.extraFieldAES=r.get(39169);u?function(e,t,n){if(e){const i=Xe(e.data);e.vendorVersion=Ne(i,0),e.vendorId=Ne(i,2);const a=Ne(i,4);e.strength=a,e.originalCompressionMethod=n,t.compressionMethod=e.compressionMethod=Ye(i,5)}else t.compressionMethod=n}(u,t,l):t.compressionMethod=l}function He(e,t,n,i,a){const r=Xe(e.data);e.version=Ne(r,0),e.signature=Je(r,1);const s=new Y;s.append(a[n]);const o=Xe(new Uint8Array(4));o.setUint32(0,s.get(),!0),e[t]=(new TextDecoder).decode(e.data.subarray(5)),e.valid=!a.bitFlag.languageEncodingFlag&&e.signature==Je(o,0),e.valid&&(i[t]=e[t],i[t+"UTF8"]=!0)}function Ke(e,t,n){return void 0===t[n]?e.options[n]:t[n]}function Ge(e,t){return t&&"cp437"!=t.trim().toLowerCase()?new TextDecoder(t).decode(e):(e=>{let t="";for(let n=0;n<e.length;n++)t+=Z[e[n]];return t})(e)}function Ze(e){const t=(4294901760&e)>>16,n=65535&e;try{return new Date(1980+((65024&t)>>9),((480&t)>>5)-1,31&t,(63488&n)>>11,(2016&n)>>5,2*(31&n),0)}catch(e){}}function Ne(e,t){return e.getUint8(t)}function Ye(e,t){return e.getUint16(t,!0)}function Je(e,t){return e.getUint32(t,!0)}function Qe(e,t){return Number(e.getBigUint64(t,!0))}function Xe(e){return new DataView(e.buffer)}function et(e,t,n){return e.readUint8Array(t,n)}(()=>{if("function"==typeof URL.createObjectURL){const e=(()=>{const e=[];for(let t=0;t<256;t++){let n=t;for(let e=0;e<8;e++)1&n?n=n>>>1^3988292384:n>>>=1;e[t]=n}class t{constructor(e){this.crc=e||-1}append(t){let n=0|this.crc;for(let i=0,a=0|t.length;i<a;i++)n=n>>>8^e[255&(n^t[i])];this.crc=n}get(){return~this.crc}}const n={concat(e,t){if(0===e.length||0===t.length)return e.concat(t);const i=e[e.length-1],a=n.getPartial(i);return 32===a?e.concat(t):n._shiftRight(t,a,0|i,e.slice(0,e.length-1))},bitLength(e){const t=e.length;if(0===t)return 0;const i=e[t-1];return 32*(t-1)+n.getPartial(i)},clamp(e,t){if(32*e.length<t)return e;const i=(e=e.slice(0,Math.ceil(t/32))).length;return t&=31,i>0&&t&&(e[i-1]=n.partial(t,e[i-1]&2147483648>>t-1,1)),e},partial:(e,t,n)=>32===e?t:(n?0|t:t<<32-e)+1099511627776*e,getPartial:e=>Math.round(e/1099511627776)||32,_shiftRight(e,t,i,a){for(void 0===a&&(a=[]);t>=32;t-=32)a.push(i),i=0;if(0===t)return a.concat(e);for(let n=0;n<e.length;n++)a.push(i|e[n]>>>t),i=e[n]<<32-t;const r=e.length?e[e.length-1]:0,s=n.getPartial(r);return a.push(n.partial(t+s&31,t+s>32?i:a.pop(),1)),a}},i={bytes:{fromBits(e){const t=n.bitLength(e)/8,i=new Uint8Array(t);let a;for(let n=0;n<t;n++)0==(3&n)&&(a=e[n/4]),i[n]=a>>>24,a<<=8;return i},toBits(e){const t=[];let i,a=0;for(i=0;i<e.length;i++)a=a<<8|e[i],3==(3&i)&&(t.push(a),a=0);return 3&i&&t.push(n.partial(8*(3&i),a)),t}}},a={sha1:function(e){e?(this._h=e._h.slice(0),this._buffer=e._buffer.slice(0),this._length=e._length):this.reset()}};a.sha1.prototype={blockSize:512,reset:function(){const e=this;return e._h=this._init.slice(0),e._buffer=[],e._length=0,e},update:function(e){const t=this;"string"==typeof e&&(e=i.utf8String.toBits(e));const a=t._buffer=n.concat(t._buffer,e),r=t._length,s=t._length=r+n.bitLength(e);if(s>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");const o=new Uint32Array(a);let l=0;for(let e=t.blockSize+r-(t.blockSize+r&t.blockSize-1);e<=s;e+=t.blockSize)t._block(o.subarray(16*l,16*(l+1))),l+=1;return a.splice(0,16*l),t},finalize:function(){const e=this;let t=e._buffer;const i=e._h;t=n.concat(t,[n.partial(1,1)]);for(let e=t.length+2;15&e;e++)t.push(0);for(t.push(Math.floor(e._length/4294967296)),t.push(0|e._length);t.length;)e._block(t.splice(0,16));return e.reset(),i},_init:[1732584193,4023233417,2562383102,271733878,3285377520],_key:[1518500249,1859775393,2400959708,3395469782],_f:function(e,t,n,i){return e<=19?t&n|~t&i:e<=39?t^n^i:e<=59?t&n|t&i|n&i:e<=79?t^n^i:void 0},_S:function(e,t){return t<<e|t>>>32-e},_block:function(e){const t=this,n=t._h,i=Array(80);for(let t=0;t<16;t++)i[t]=e[t];let a=n[0],r=n[1],s=n[2],o=n[3],l=n[4];for(let e=0;e<=79;e++){e>=16&&(i[e]=t._S(1,i[e-3]^i[e-8]^i[e-14]^i[e-16]));const n=t._S(5,a)+t._f(e,r,s,o)+l+i[e]+t._key[Math.floor(e/20)]|0;l=o,o=s,s=t._S(30,r),r=a,a=n}n[0]=n[0]+a|0,n[1]=n[1]+r|0,n[2]=n[2]+s|0,n[3]=n[3]+o|0,n[4]=n[4]+l|0}};const r={name:"PBKDF2"},s=Object.assign({hash:{name:"HMAC"}},r),o=Object.assign({iterations:1e3,hash:{name:"SHA-1"}},r),l=["deriveBits"],d=[8,12,16],c=[16,24,32],f=[0,0,0,0],u=crypto.subtle,p=i.bytes,m=class{constructor(e){const t=this;t._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],t._tables[0][0][0]||t._precompute();const n=t._tables[0][4],i=t._tables[1],a=e.length;let r,s,o,l=1;if(4!==a&&6!==a&&8!==a)throw new Error("invalid aes key size");for(t._key=[s=e.slice(0),o=[]],r=a;r<4*a+28;r++){let e=s[r-1];(r%a==0||8===a&&r%a==4)&&(e=n[e>>>24]<<24^n[e>>16&255]<<16^n[e>>8&255]<<8^n[255&e],r%a==0&&(e=e<<8^e>>>24^l<<24,l=l<<1^283*(l>>7))),s[r]=s[r-a]^e}for(let e=0;r;e++,r--){const t=s[3&e?r:r-4];o[e]=r<=4||e<4?t:i[0][n[t>>>24]]^i[1][n[t>>16&255]]^i[2][n[t>>8&255]]^i[3][n[255&t]]}}encrypt(e){return this._crypt(e,0)}decrypt(e){return this._crypt(e,1)}_precompute(){const e=this._tables[0],t=this._tables[1],n=e[4],i=t[4],a=[],r=[];let s,o,l,d;for(let e=0;e<256;e++)r[(a[e]=e<<1^283*(e>>7))^e]=e;for(let c=s=0;!n[c];c^=o||1,s=r[s]||1){let r=s^s<<1^s<<2^s<<3^s<<4;r=r>>8^255&r^99,n[c]=r,i[r]=c,d=a[l=a[o=a[c]]];let f=16843009*d^65537*l^257*o^16843008*c,u=257*a[r]^16843008*r;for(let n=0;n<4;n++)e[n][c]=u=u<<24^u>>>8,t[n][r]=f=f<<24^f>>>8}for(let n=0;n<5;n++)e[n]=e[n].slice(0),t[n]=t[n].slice(0)}_crypt(e,t){if(4!==e.length)throw new Error("invalid aes block size");const n=this._key[t],i=n.length/4-2,a=[0,0,0,0],r=this._tables[t],s=r[0],o=r[1],l=r[2],d=r[3],c=r[4];let f,u,p,m=e[0]^n[0],_=e[t?3:1]^n[1],h=e[2]^n[2],x=e[t?1:3]^n[3],w=4;for(let e=0;e<i;e++)f=s[m>>>24]^o[_>>16&255]^l[h>>8&255]^d[255&x]^n[w],u=s[_>>>24]^o[h>>16&255]^l[x>>8&255]^d[255&m]^n[w+1],p=s[h>>>24]^o[x>>16&255]^l[m>>8&255]^d[255&_]^n[w+2],x=s[x>>>24]^o[m>>16&255]^l[_>>8&255]^d[255&h]^n[w+3],w+=4,m=f,_=u,h=p;for(let e=0;e<4;e++)a[t?3&-e:e]=c[m>>>24]<<24^c[_>>16&255]<<16^c[h>>8&255]<<8^c[255&x]^n[w++],f=m,m=_,_=h,h=x,x=f;return a}},_=class{constructor(e,t){this._prf=e,this._initIv=t,this._iv=t}reset(){this._iv=this._initIv}update(e){return this.calculate(this._prf,e,this._iv)}incWord(e){if(255==(e>>24&255)){let t=e>>16&255,n=e>>8&255,i=255&e;255===t?(t=0,255===n?(n=0,255===i?i=0:++i):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=i}else e+=1<<24;return e}incCounter(e){0===(e[0]=this.incWord(e[0]))&&(e[1]=this.incWord(e[1]))}calculate(e,t,i){let a;if(!(a=t.length))return[];const r=n.bitLength(t);for(let n=0;n<a;n+=4){this.incCounter(i);const a=e.encrypt(i);t[n]^=a[0],t[n+1]^=a[1],t[n+2]^=a[2],t[n+3]^=a[3]}return n.clamp(t,r)}},h=class{constructor(e){const t=this,n=t._hash=a.sha1,i=[[],[]],r=n.prototype.blockSize/32;t._baseHash=[new n,new n],e.length>r&&(e=n.hash(e));for(let t=0;t<r;t++)i[0][t]=909522486^e[t],i[1][t]=1549556828^e[t];t._baseHash[0].update(i[0]),t._baseHash[1].update(i[1]),t._resultHash=new n(t._baseHash[0])}reset(){const e=this;e._resultHash=new e._hash(e._baseHash[0]),e._updated=!1}update(e){this._updated=!0,this._resultHash.update(e)}digest(){const e=this,t=e._resultHash.finalize(),n=new e._hash(e._baseHash[1]).update(t).finalize();return e.reset(),n}};class x{constructor(e,t,n){Object.assign(this,{password:e,signed:t,strength:n-1,pendingInput:new Uint8Array(0)})}async append(e){const t=this;if(t.password){const n=y(e,0,d[t.strength]+2);await async function(e,t,n){await v(e,n,y(t,0,d[e.strength]));const i=y(t,d[e.strength]),a=e.keys.passwordVerification;if(a[0]!=i[0]||a[1]!=i[1])throw new Error("Invalid pasword")}(t,n,t.password),t.password=null,t.aesCtrGladman=new _(new m(t.keys.key),Array.from(f)),t.hmac=new h(t.keys.authentication),e=y(e,d[t.strength]+2)}return b(t,e,new Uint8Array(e.length-10-(e.length-10)%16),0,10,!0)}async flush(){const e=this,t=e.pendingInput,n=y(t,0,t.length-10),i=y(t,t.length-10);let a=new Uint8Array(0);if(n.length){const t=p.toBits(n);e.hmac.update(t);const i=e.aesCtrGladman.update(t);a=p.fromBits(i)}let r=!0;if(e.signed){const t=y(p.fromBits(e.hmac.digest()),0,10);for(let e=0;e<10;e++)t[e]!=i[e]&&(r=!1)}return{valid:r,data:a}}}class w{constructor(e,t){Object.assign(this,{password:e,strength:t-1,pendingInput:new Uint8Array(0)})}async append(e){const t=this;let n=new Uint8Array(0);t.password&&(n=await async function(e,t){const n=crypto.getRandomValues(new Uint8Array(d[e.strength]));return await v(e,t,n),g(n,e.keys.passwordVerification)}(t,t.password),t.password=null,t.aesCtrGladman=new _(new m(t.keys.key),Array.from(f)),t.hmac=new h(t.keys.authentication));const i=new Uint8Array(n.length+e.length-e.length%16);return i.set(n,0),b(t,e,i,n.length,0)}async flush(){const e=this;let t=new Uint8Array(0);if(e.pendingInput.length){const n=e.aesCtrGladman.update(p.toBits(e.pendingInput));e.hmac.update(n),t=p.fromBits(n)}const n=y(p.fromBits(e.hmac.digest()),0,10);return{data:g(t,n),signature:n}}}function b(e,t,n,i,a,r){const s=t.length-a;let o;for(e.pendingInput.length&&(t=g(e.pendingInput,t),n=function(e,t){if(t&&t>e.length){const n=e;(e=new Uint8Array(t)).set(n,0)}return e}(n,s-s%16)),o=0;o<=s-16;o+=16){const a=p.toBits(y(t,o,o+16));r&&e.hmac.update(a);const s=e.aesCtrGladman.update(a);r||e.hmac.update(s),n.set(p.fromBits(s),o+i)}return e.pendingInput=y(t,o),n}async function v(e,t,n){const i=(new TextEncoder).encode(t),a=await u.importKey("raw",i,s,!1,l),r=await u.deriveBits(Object.assign({salt:n},o),a,8*(2*c[e.strength]+2)),d=new Uint8Array(r);e.keys={key:p.toBits(y(d,0,c[e.strength])),authentication:p.toBits(y(d,c[e.strength],2*c[e.strength])),passwordVerification:y(d,2*c[e.strength])}}function g(e,t){let n=e;return e.length+t.length&&(n=new Uint8Array(e.length+t.length),n.set(e,0),n.set(t,e.length)),n}function y(e,t,n){return e.subarray(t,n)}class k{constructor(e,t){Object.assign(this,{password:e,passwordVerification:t}),S(this,e)}async append(e){const t=this;if(t.password){const n=U(t,e.subarray(0,12));if(t.password=null,n[11]!=t.passwordVerification)throw new Error("Invalid pasword");e=e.subarray(12)}return U(t,e)}async flush(){return{valid:!0,data:new Uint8Array(0)}}}class z{constructor(e,t){Object.assign(this,{password:e,passwordVerification:t}),S(this,e)}async append(e){const t=this;let n,i;if(t.password){t.password=null;const a=crypto.getRandomValues(new Uint8Array(12));a[11]=t.passwordVerification,n=new Uint8Array(e.length+a.length),n.set(A(t,a),0),i=12}else n=new Uint8Array(e.length),i=0;return n.set(A(t,e),i),n}async flush(){return{data:new Uint8Array(0)}}}function U(e,t){const n=new Uint8Array(t.length);for(let i=0;i<t.length;i++)n[i]=E(e)^t[i],j(e,n[i]);return n}function A(e,t){const n=new Uint8Array(t.length);for(let i=0;i<t.length;i++)n[i]=E(e)^t[i],j(e,t[i]);return n}function S(e,n){e.keys=[305419896,591751049,878082192],e.crcKey0=new t(e.keys[0]),e.crcKey2=new t(e.keys[2]);for(let t=0;t<n.length;t++)j(e,n.charCodeAt(t))}function j(e,t){e.crcKey0.append([t]),e.keys[0]=~e.crcKey0.get(),e.keys[1]=I(e.keys[1]+C(e.keys[0])),e.keys[1]=I(Math.imul(e.keys[1],134775813)+1),e.crcKey2.append([e.keys[1]>>>24]),e.keys[2]=~e.crcKey2.get()}function E(e){const t=2|e.keys[2];return C(Math.imul(t,1^t)>>>8)}function C(e){return 255&e}function I(e){return 4294967295&e}class F{constructor(e,{signature:n,password:i,signed:a,compressed:r,zipCrypto:s,passwordVerification:o,encryptionStrength:l},{chunkSize:d}){const c=Boolean(i);Object.assign(this,{signature:n,encrypted:c,signed:a,compressed:r,inflate:r&&new e({chunkSize:d}),crc32:a&&new t,zipCrypto:s,decrypt:c&&s?new k(i,o):new x(i,a,l)})}async append(e){const t=this;return t.encrypted&&e.length&&(e=await t.decrypt.append(e)),t.compressed&&e.length&&(e=await t.inflate.append(e)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),e}async flush(){const e=this;let t,n=new Uint8Array(0);if(e.encrypted){const t=await e.decrypt.flush();if(!t.valid)throw new Error("Invalid signature");n=t.data}if((!e.encrypted||e.zipCrypto)&&e.signed){const n=new DataView(new Uint8Array(4).buffer);if(t=e.crc32.get(),n.setUint32(0,t),e.cipher!=n.getUint32(0,!1))throw new Error("Invalid signature")}return e.compressed&&(n=await e.inflate.append(n)||new Uint8Array(0),await e.inflate.flush()),{data:n,signature:t}}}class B{constructor(e,{encrypted:n,signed:i,compressed:a,level:r,zipCrypto:s,password:o,passwordVerification:l,encryptionStrength:d},{chunkSize:c}){Object.assign(this,{encrypted:n,signed:i,compressed:a,deflate:a&&new e({level:r||5,chunkSize:c}),crc32:i&&new t,zipCrypto:s,encrypt:n&&s?new z(o,l):new w(o,d)})}async append(e){const t=this;let n=e;return t.compressed&&e.length&&(n=await t.deflate.append(e)),t.encrypted&&n.length&&(n=await t.encrypt.append(n)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),n}async flush(){const e=this;let t,n=new Uint8Array(0);if(e.compressed&&(n=await e.deflate.flush()||new Uint8Array(0)),e.encrypted){n=await e.encrypt.append(n);const i=await e.encrypt.flush();t=i.signature;const a=new Uint8Array(n.length+i.data.length);a.set(n,0),a.set(i.data,n.length),n=a}return e.encrypted&&!e.zipCrypto||!e.signed||(t=e.crc32.get()),{data:n,signature:t}}}const $={init(e){e.scripts&&e.scripts.length&&importScripts.apply(void 0,e.scripts);const t=e.options;let n;self.initCodec&&self.initCodec(),t.codecType.startsWith("deflate")?n=self.Deflate:t.codecType.startsWith("inflate")&&(n=self.Inflate),q=function(e,t,n){return t.codecType.startsWith("deflate")?new B(e,t,n):t.codecType.startsWith("inflate")?new F(e,t,n):void 0}(n,t,e.config)},append:async e=>({data:await q.append(e.data)}),flush:()=>q.flush()};let q;function D(e){return e.map((([e,t])=>new Array(e).fill(t,0,e))).flat()}addEventListener("message",(async e=>{const t=e.data,n=t.type,i=$[n];if(i)try{t.data&&(t.data=new Uint8Array(t.data));const e=await i(t)||{};if(e.type=n,e.data)try{e.data=e.data.buffer,postMessage(e,[e.data])}catch(t){postMessage(e)}else postMessage(e)}catch(e){postMessage({type:n,error:{message:e.message,stack:e.stack}})}}));const M=[0,1,2,3].concat(...D([[2,4],[2,5],[4,6],[4,7],[8,8],[8,9],[16,10],[16,11],[32,12],[32,13],[64,14],[64,15],[2,0],[1,16],[1,17],[2,18],[2,19],[4,20],[4,21],[8,22],[8,23],[16,24],[16,25],[32,26],[32,27],[64,28],[64,29]]));function L(){const e=this;function t(e,t){let n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}e.build_tree=function(n){const i=e.dyn_tree,a=e.stat_desc.static_tree,r=e.stat_desc.elems;let s,o,l,d=-1;for(n.heap_len=0,n.heap_max=573,s=0;s<r;s++)0!==i[2*s]?(n.heap[++n.heap_len]=d=s,n.depth[s]=0):i[2*s+1]=0;for(;n.heap_len<2;)l=n.heap[++n.heap_len]=d<2?++d:0,i[2*l]=1,n.depth[l]=0,n.opt_len--,a&&(n.static_len-=a[2*l+1]);for(e.max_code=d,s=Math.floor(n.heap_len/2);s>=1;s--)n.pqdownheap(i,s);l=r;do{s=n.heap[1],n.heap[1]=n.heap[n.heap_len--],n.pqdownheap(i,1),o=n.heap[1],n.heap[--n.heap_max]=s,n.heap[--n.heap_max]=o,i[2*l]=i[2*s]+i[2*o],n.depth[l]=Math.max(n.depth[s],n.depth[o])+1,i[2*s+1]=i[2*o+1]=l,n.heap[1]=l++,n.pqdownheap(i,1)}while(n.heap_len>=2);n.heap[--n.heap_max]=n.heap[1],function(t){const n=e.dyn_tree,i=e.stat_desc.static_tree,a=e.stat_desc.extra_bits,r=e.stat_desc.extra_base,s=e.stat_desc.max_length;let o,l,d,c,f,u,p=0;for(c=0;c<=15;c++)t.bl_count[c]=0;for(n[2*t.heap[t.heap_max]+1]=0,o=t.heap_max+1;o<573;o++)l=t.heap[o],c=n[2*n[2*l+1]+1]+1,c>s&&(c=s,p++),n[2*l+1]=c,l>e.max_code||(t.bl_count[c]++,f=0,l>=r&&(f=a[l-r]),u=n[2*l],t.opt_len+=u*(c+f),i&&(t.static_len+=u*(i[2*l+1]+f)));if(0!==p){do{for(c=s-1;0===t.bl_count[c];)c--;t.bl_count[c]--,t.bl_count[c+1]+=2,t.bl_count[s]--,p-=2}while(p>0);for(c=s;0!==c;c--)for(l=t.bl_count[c];0!==l;)d=t.heap[--o],d>e.max_code||(n[2*d+1]!=c&&(t.opt_len+=(c-n[2*d+1])*n[2*d],n[2*d+1]=c),l--)}}(n),function(e,n,i){const a=[];let r,s,o,l=0;for(r=1;r<=15;r++)a[r]=l=l+i[r-1]<<1;for(s=0;s<=n;s++)o=e[2*s+1],0!==o&&(e[2*s]=t(a[o]++,o))}(i,e.max_code,n.bl_count)}}function R(e,t,n,i,a){const r=this;r.static_tree=e,r.extra_bits=t,r.extra_base=n,r.elems=i,r.max_length=a}function O(e,t,n,i,a){const r=this;r.good_length=e,r.max_lazy=t,r.nice_length=n,r.max_chain=i,r.func=a}L._length_code=[0,1,2,3,4,5,6,7].concat(...D([[2,8],[2,9],[2,10],[2,11],[4,12],[4,13],[4,14],[4,15],[8,16],[8,17],[8,18],[8,19],[16,20],[16,21],[16,22],[16,23],[32,24],[32,25],[32,26],[31,27],[1,28]])),L.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],L.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],L.d_code=function(e){return e<256?M[e]:M[256+(e>>>7)]},L.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],L.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],L.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],L.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],R.static_ltree=[12,8,140,8,76,8,204,8,44,8,172,8,108,8,236,8,28,8,156,8,92,8,220,8,60,8,188,8,124,8,252,8,2,8,130,8,66,8,194,8,34,8,162,8,98,8,226,8,18,8,146,8,82,8,210,8,50,8,178,8,114,8,242,8,10,8,138,8,74,8,202,8,42,8,170,8,106,8,234,8,26,8,154,8,90,8,218,8,58,8,186,8,122,8,250,8,6,8,134,8,70,8,198,8,38,8,166,8,102,8,230,8,22,8,150,8,86,8,214,8,54,8,182,8,118,8,246,8,14,8,142,8,78,8,206,8,46,8,174,8,110,8,238,8,30,8,158,8,94,8,222,8,62,8,190,8,126,8,254,8,1,8,129,8,65,8,193,8,33,8,161,8,97,8,225,8,17,8,145,8,81,8,209,8,49,8,177,8,113,8,241,8,9,8,137,8,73,8,201,8,41,8,169,8,105,8,233,8,25,8,153,8,89,8,217,8,57,8,185,8,121,8,249,8,5,8,133,8,69,8,197,8,37,8,165,8,101,8,229,8,21,8,149,8,85,8,213,8,53,8,181,8,117,8,245,8,13,8,141,8,77,8,205,8,45,8,173,8,109,8,237,8,29,8,157,8,93,8,221,8,61,8,189,8,125,8,253,8,19,9,275,9,147,9,403,9,83,9,339,9,211,9,467,9,51,9,307,9,179,9,435,9,115,9,371,9,243,9,499,9,11,9,267,9,139,9,395,9,75,9,331,9,203,9,459,9,43,9,299,9,171,9,427,9,107,9,363,9,235,9,491,9,27,9,283,9,155,9,411,9,91,9,347,9,219,9,475,9,59,9,315,9,187,9,443,9,123,9,379,9,251,9,507,9,7,9,263,9,135,9,391,9,71,9,327,9,199,9,455,9,39,9,295,9,167,9,423,9,103,9,359,9,231,9,487,9,23,9,279,9,151,9,407,9,87,9,343,9,215,9,471,9,55,9,311,9,183,9,439,9,119,9,375,9,247,9,503,9,15,9,271,9,143,9,399,9,79,9,335,9,207,9,463,9,47,9,303,9,175,9,431,9,111,9,367,9,239,9,495,9,31,9,287,9,159,9,415,9,95,9,351,9,223,9,479,9,63,9,319,9,191,9,447,9,127,9,383,9,255,9,511,9,0,7,64,7,32,7,96,7,16,7,80,7,48,7,112,7,8,7,72,7,40,7,104,7,24,7,88,7,56,7,120,7,4,7,68,7,36,7,100,7,20,7,84,7,52,7,116,7,3,8,131,8,67,8,195,8,35,8,163,8,99,8,227,8],R.static_dtree=[0,5,16,5,8,5,24,5,4,5,20,5,12,5,28,5,2,5,18,5,10,5,26,5,6,5,22,5,14,5,30,5,1,5,17,5,9,5,25,5,5,5,21,5,13,5,29,5,3,5,19,5,11,5,27,5,7,5,23,5],R.static_l_desc=new R(R.static_ltree,L.extra_lbits,257,286,15),R.static_d_desc=new R(R.static_dtree,L.extra_dbits,0,30,15),R.static_bl_desc=new R(null,L.extra_blbits,0,19,7);const V=[new O(0,0,0,0,0),new O(4,4,8,4,1),new O(4,5,16,8,1),new O(4,6,32,32,1),new O(4,4,16,16,2),new O(8,16,32,32,2),new O(8,16,128,128,2),new O(8,32,128,256,2),new O(32,128,258,1024,2),new O(32,258,258,4096,2)],W=["need dictionary","stream end","","","stream error","data error","","buffer error","",""];function T(e,t,n,i){const a=e[2*t],r=e[2*n];return a<r||a==r&&i[t]<=i[n]}function P(){const e=this;let t,n,i,a,r,s,o,l,d,c,f,u,p,m,_,h,x,w,b,v,g,y,k,z,U,A,S,j,E,C,I,F,B;const $=new L,q=new L,D=new L;let M,O,P,H,K,G,Z,N;function Y(){let t;for(t=0;t<286;t++)I[2*t]=0;for(t=0;t<30;t++)F[2*t]=0;for(t=0;t<19;t++)B[2*t]=0;I[512]=1,e.opt_len=e.static_len=0,P=K=0}function J(e,t){let n,i=-1,a=e[1],r=0,s=7,o=4;0===a&&(s=138,o=3),e[2*(t+1)+1]=65535;for(let l=0;l<=t;l++)n=a,a=e[2*(l+1)+1],++r<s&&n==a||(r<o?B[2*n]+=r:0!==n?(n!=i&&B[2*n]++,B[32]++):r<=10?B[34]++:B[36]++,r=0,i=n,0===a?(s=138,o=3):n==a?(s=6,o=3):(s=7,o=4))}function Q(t){e.pending_buf[e.pending++]=t}function X(e){Q(255&e),Q(e>>>8&255)}function ee(e,t){let n;const i=t;N>16-i?(n=e,Z|=n<<N&65535,X(Z),Z=n>>>16-N,N+=i-16):(Z|=e<<N&65535,N+=i)}function te(e,t){const n=2*e;ee(65535&t[n],65535&t[n+1])}function ne(e,t){let n,i,a=-1,r=e[1],s=0,o=7,l=4;for(0===r&&(o=138,l=3),n=0;n<=t;n++)if(i=r,r=e[2*(n+1)+1],!(++s<o&&i==r)){if(s<l)do{te(i,B)}while(0!=--s);else 0!==i?(i!=a&&(te(i,B),s--),te(16,B),ee(s-3,2)):s<=10?(te(17,B),ee(s-3,3)):(te(18,B),ee(s-11,7));s=0,a=i,0===r?(o=138,l=3):i==r?(o=6,l=3):(o=7,l=4)}}function ie(){16==N?(X(Z),Z=0,N=0):N>=8&&(Q(255&Z),Z>>>=8,N-=8)}function ae(t,n){let i,a,r;if(e.pending_buf[H+2*P]=t>>>8&255,e.pending_buf[H+2*P+1]=255&t,e.pending_buf[M+P]=255&n,P++,0===t?I[2*n]++:(K++,t--,I[2*(L._length_code[n]+256+1)]++,F[2*L.d_code(t)]++),0==(8191&P)&&S>2){for(i=8*P,a=g-x,r=0;r<30;r++)i+=F[2*r]*(5+L.extra_dbits[r]);if(i>>>=3,K<Math.floor(P/2)&&i<Math.floor(a/2))return!0}return P==O-1}function re(t,n){let i,a,r,s,o=0;if(0!==P)do{i=e.pending_buf[H+2*o]<<8&65280|255&e.pending_buf[H+2*o+1],a=255&e.pending_buf[M+o],o++,0===i?te(a,t):(r=L._length_code[a],te(r+256+1,t),s=L.extra_lbits[r],0!==s&&(a-=L.base_length[r],ee(a,s)),i--,r=L.d_code(i),te(r,n),s=L.extra_dbits[r],0!==s&&(i-=L.base_dist[r],ee(i,s)))}while(o<P);te(256,t),G=t[513]}function se(){N>8?X(Z):N>0&&Q(255&Z),Z=0,N=0}function oe(t,n,i){ee(0+(i?1:0),3),function(t,n,i){se(),G=8,X(n),X(~n),e.pending_buf.set(l.subarray(t,t+n),e.pending),e.pending+=n}(t,n)}function le(t,n,i){let a,r,s=0;S>0?($.build_tree(e),q.build_tree(e),s=function(){let t;for(J(I,$.max_code),J(F,q.max_code),D.build_tree(e),t=18;t>=3&&0===B[2*L.bl_order[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(),a=e.opt_len+3+7>>>3,r=e.static_len+3+7>>>3,r<=a&&(a=r)):a=r=n+5,n+4<=a&&-1!=t?oe(t,n,i):r==a?(ee(2+(i?1:0),3),re(R.static_ltree,R.static_dtree)):(ee(4+(i?1:0),3),function(e,t,n){let i;for(ee(e-257,5),ee(t-1,5),ee(n-4,4),i=0;i<n;i++)ee(B[2*L.bl_order[i]+1],3);ne(I,e-1),ne(F,t-1)}($.max_code+1,q.max_code+1,s+1),re(I,F)),Y(),i&&se()}function de(e){le(x>=0?x:-1,g-x,e),x=g,t.flush_pending()}function ce(){let e,n,i,a;do{if(a=d-k-g,0===a&&0===g&&0===k)a=r;else if(-1==a)a--;else if(g>=r+r-262){l.set(l.subarray(r,r+r),0),y-=r,g-=r,x-=r,e=p,i=e;do{n=65535&f[--i],f[i]=n>=r?n-r:0}while(0!=--e);e=r,i=e;do{n=65535&c[--i],c[i]=n>=r?n-r:0}while(0!=--e);a+=r}if(0===t.avail_in)return;e=t.read_buf(l,g+k,a),k+=e,k>=3&&(u=255&l[g],u=(u<<h^255&l[g+1])&_)}while(k<262&&0!==t.avail_in)}function fe(e){let t,n,i=U,a=g,s=z;const d=g>r-262?g-(r-262):0;let f=C;const u=o,p=g+258;let m=l[a+s-1],_=l[a+s];z>=E&&(i>>=2),f>k&&(f=k);do{if(t=e,l[t+s]==_&&l[t+s-1]==m&&l[t]==l[a]&&l[++t]==l[a+1]){a+=2,t++;do{}while(l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&l[++a]==l[++t]&&a<p);if(n=258-(p-a),a=p-258,n>s){if(y=e,s=n,n>=f)break;m=l[a+s-1],_=l[a+s]}}}while((e=65535&c[e&u])>d&&0!=--i);return s<=k?s:k}function ue(t){return t.total_in=t.total_out=0,t.msg=null,e.pending=0,e.pending_out=0,n=113,a=0,$.dyn_tree=I,$.stat_desc=R.static_l_desc,q.dyn_tree=F,q.stat_desc=R.static_d_desc,D.dyn_tree=B,D.stat_desc=R.static_bl_desc,Z=0,N=0,G=8,Y(),function(){d=2*r,f[p-1]=0;for(let e=0;e<p-1;e++)f[e]=0;A=V[S].max_lazy,E=V[S].good_length,C=V[S].nice_length,U=V[S].max_chain,g=0,x=0,k=0,w=z=2,v=0,u=0}(),0}e.depth=[],e.bl_count=[],e.heap=[],I=[],F=[],B=[],e.pqdownheap=function(t,n){const i=e.heap,a=i[n];let r=n<<1;for(;r<=e.heap_len&&(r<e.heap_len&&T(t,i[r+1],i[r],e.depth)&&r++,!T(t,a,i[r],e.depth));)i[n]=i[r],n=r,r<<=1;i[n]=a},e.deflateInit=function(t,n,a,d,u,x){return d||(d=8),u||(u=8),x||(x=0),t.msg=null,-1==n&&(n=6),u<1||u>9||8!=d||a<9||a>15||n<0||n>9||x<0||x>2?-2:(t.dstate=e,s=a,r=1<<s,o=r-1,m=u+7,p=1<<m,_=p-1,h=Math.floor((m+3-1)/3),l=new Uint8Array(2*r),c=[],f=[],O=1<<u+6,e.pending_buf=new Uint8Array(4*O),i=4*O,H=Math.floor(O/2),M=3*O,S=n,j=x,ue(t))},e.deflateEnd=function(){return 42!=n&&113!=n&&666!=n?-2:(e.pending_buf=null,f=null,c=null,l=null,e.dstate=null,113==n?-3:0)},e.deflateParams=function(e,t,n){let i=0;return-1==t&&(t=6),t<0||t>9||n<0||n>2?-2:(V[S].func!=V[t].func&&0!==e.total_in&&(i=e.deflate(1)),S!=t&&(S=t,A=V[S].max_lazy,E=V[S].good_length,C=V[S].nice_length,U=V[S].max_chain),j=n,i)},e.deflateSetDictionary=function(e,t,i){let a,s=i,d=0;if(!t||42!=n)return-2;if(s<3)return 0;for(s>r-262&&(s=r-262,d=i-s),l.set(t.subarray(d,d+s),0),g=s,x=s,u=255&l[0],u=(u<<h^255&l[1])&_,a=0;a<=s-3;a++)u=(u<<h^255&l[a+2])&_,c[a&o]=f[u],f[u]=a;return 0},e.deflate=function(d,m){let U,E,C,I,F;if(m>4||m<0)return-2;if(!d.next_out||!d.next_in&&0!==d.avail_in||666==n&&4!=m)return d.msg=W[4],-2;if(0===d.avail_out)return d.msg=W[7],-5;var B;if(t=d,I=a,a=m,42==n&&(E=8+(s-8<<4)<<8,C=(S-1&255)>>1,C>3&&(C=3),E|=C<<6,0!==g&&(E|=32),E+=31-E%31,n=113,Q((B=E)>>8&255),Q(255&B)),0!==e.pending){if(t.flush_pending(),0===t.avail_out)return a=-1,0}else if(0===t.avail_in&&m<=I&&4!=m)return t.msg=W[7],-5;if(666==n&&0!==t.avail_in)return d.msg=W[7],-5;if(0!==t.avail_in||0!==k||0!=m&&666!=n){switch(F=-1,V[S].func){case 0:F=function(e){let n,a=65535;for(a>i-5&&(a=i-5);;){if(k<=1){if(ce(),0===k&&0==e)return 0;if(0===k)break}if(g+=k,k=0,n=x+a,(0===g||g>=n)&&(k=g-n,g=n,de(!1),0===t.avail_out))return 0;if(g-x>=r-262&&(de(!1),0===t.avail_out))return 0}return de(4==e),0===t.avail_out?4==e?2:0:4==e?3:1}(m);break;case 1:F=function(e){let n,i=0;for(;;){if(k<262){if(ce(),k<262&&0==e)return 0;if(0===k)break}if(k>=3&&(u=(u<<h^255&l[g+2])&_,i=65535&f[u],c[g&o]=f[u],f[u]=g),0!==i&&(g-i&65535)<=r-262&&2!=j&&(w=fe(i)),w>=3)if(n=ae(g-y,w-3),k-=w,w<=A&&k>=3){w--;do{g++,u=(u<<h^255&l[g+2])&_,i=65535&f[u],c[g&o]=f[u],f[u]=g}while(0!=--w);g++}else g+=w,w=0,u=255&l[g],u=(u<<h^255&l[g+1])&_;else n=ae(0,255&l[g]),k--,g++;if(n&&(de(!1),0===t.avail_out))return 0}return de(4==e),0===t.avail_out?4==e?2:0:4==e?3:1}(m);break;case 2:F=function(e){let n,i,a=0;for(;;){if(k<262){if(ce(),k<262&&0==e)return 0;if(0===k)break}if(k>=3&&(u=(u<<h^255&l[g+2])&_,a=65535&f[u],c[g&o]=f[u],f[u]=g),z=w,b=y,w=2,0!==a&&z<A&&(g-a&65535)<=r-262&&(2!=j&&(w=fe(a)),w<=5&&(1==j||3==w&&g-y>4096)&&(w=2)),z>=3&&w<=z){i=g+k-3,n=ae(g-1-b,z-3),k-=z-1,z-=2;do{++g<=i&&(u=(u<<h^255&l[g+2])&_,a=65535&f[u],c[g&o]=f[u],f[u]=g)}while(0!=--z);if(v=0,w=2,g++,n&&(de(!1),0===t.avail_out))return 0}else if(0!==v){if(n=ae(0,255&l[g-1]),n&&de(!1),g++,k--,0===t.avail_out)return 0}else v=1,g++,k--}return 0!==v&&(n=ae(0,255&l[g-1]),v=0),de(4==e),0===t.avail_out?4==e?2:0:4==e?3:1}(m)}if(2!=F&&3!=F||(n=666),0==F||2==F)return 0===t.avail_out&&(a=-1),0;if(1==F){if(1==m)ee(2,3),te(256,R.static_ltree),ie(),1+G+10-N<9&&(ee(2,3),te(256,R.static_ltree),ie()),G=7;else if(oe(0,0,!1),3==m)for(U=0;U<p;U++)f[U]=0;if(t.flush_pending(),0===t.avail_out)return a=-1,0}}return 4!=m?0:1}}function H(){const e=this;e.next_in_index=0,e.next_out_index=0,e.avail_in=0,e.total_in=0,e.avail_out=0,e.total_out=0}function K(e){const t=new H,n=e&&e.chunkSize?Math.floor(1.05*e.chunkSize):65536,i=new Uint8Array(n);let a=e?e.level:-1;void 0===a&&(a=-1),t.deflateInit(a),t.next_out=i,this.append=function(e,a){let r,s,o=0,l=0,d=0;const c=[];if(e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,r=t.deflate(0),0!=r)throw new Error("deflating: "+t.msg);t.next_out_index&&(t.next_out_index==n?c.push(new Uint8Array(i)):c.push(i.slice(0,t.next_out_index))),d+=t.next_out_index,a&&t.next_in_index>0&&t.next_in_index!=o&&(a(t.next_in_index),o=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return c.length>1?(s=new Uint8Array(d),c.forEach((function(e){s.set(e,l),l+=e.length}))):s=c[0]||new Uint8Array(0),s}},this.flush=function(){let e,a,r=0,s=0;const o=[];do{if(t.next_out_index=0,t.avail_out=n,e=t.deflate(4),1!=e&&0!=e)throw new Error("deflating: "+t.msg);n-t.avail_out>0&&o.push(i.slice(0,t.next_out_index)),s+=t.next_out_index}while(t.avail_in>0||0===t.avail_out);return t.deflateEnd(),a=new Uint8Array(s),o.forEach((function(e){a.set(e,r),r+=e.length})),a}}H.prototype={deflateInit:function(e,t){const n=this;return n.dstate=new P,t||(t=15),n.dstate.deflateInit(n,e,t)},deflate:function(e){const t=this;return t.dstate?t.dstate.deflate(t,e):-2},deflateEnd:function(){const e=this;if(!e.dstate)return-2;const t=e.dstate.deflateEnd();return e.dstate=null,t},deflateParams:function(e,t){const n=this;return n.dstate?n.dstate.deflateParams(n,e,t):-2},deflateSetDictionary:function(e,t){const n=this;return n.dstate?n.dstate.deflateSetDictionary(n,e,t):-2},read_buf:function(e,t,n){const i=this;let a=i.avail_in;return a>n&&(a=n),0===a?0:(i.avail_in-=a,e.set(i.next_in.subarray(i.next_in_index,i.next_in_index+a),t),i.next_in_index+=a,i.total_in+=a,a)},flush_pending:function(){const e=this;let t=e.dstate.pending;t>e.avail_out&&(t=e.avail_out),0!==t&&(e.next_out.set(e.dstate.pending_buf.subarray(e.dstate.pending_out,e.dstate.pending_out+t),e.next_out_index),e.next_out_index+=t,e.dstate.pending_out+=t,e.total_out+=t,e.avail_out-=t,e.dstate.pending-=t,0===e.dstate.pending&&(e.dstate.pending_out=0))}};const G=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],Z=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],N=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],Y=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],J=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],Q=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],X=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function ee(){let e,t,n,i,a,r;function s(e,t,s,o,l,d,c,f,u,p,m){let _,h,x,w,b,v,g,y,k,z,U,A,S,j,E;z=0,b=s;do{n[e[t+z]]++,z++,b--}while(0!==b);if(n[0]==s)return c[0]=-1,f[0]=0,0;for(y=f[0],v=1;v<=15&&0===n[v];v++);for(g=v,y<v&&(y=v),b=15;0!==b&&0===n[b];b--);for(x=b,y>b&&(y=b),f[0]=y,j=1<<v;v<b;v++,j<<=1)if((j-=n[v])<0)return-3;if((j-=n[b])<0)return-3;for(n[b]+=j,r[1]=v=0,z=1,S=2;0!=--b;)r[S]=v+=n[z],S++,z++;b=0,z=0;do{0!==(v=e[t+z])&&(m[r[v]++]=b),z++}while(++b<s);for(s=r[x],r[0]=b=0,z=0,w=-1,A=-y,a[0]=0,U=0,E=0;g<=x;g++)for(_=n[g];0!=_--;){for(;g>A+y;){if(w++,A+=y,E=x-A,E=E>y?y:E,(h=1<<(v=g-A))>_+1&&(h-=_+1,S=g,v<E))for(;++v<E&&!((h<<=1)<=n[++S]);)h-=n[S];if(E=1<<v,p[0]+E>1440)return-3;a[w]=U=p[0],p[0]+=E,0!==w?(r[w]=b,i[0]=v,i[1]=y,v=b>>>A-y,i[2]=U-a[w-1]-v,u.set(i,3*(a[w-1]+v))):c[0]=U}for(i[1]=g-A,z>=s?i[0]=192:m[z]<o?(i[0]=m[z]<256?0:96,i[2]=m[z++]):(i[0]=d[m[z]-o]+16+64,i[2]=l[m[z++]-o]),h=1<<g-A,v=b>>>A;v<E;v+=h)u.set(i,3*(U+v));for(v=1<<g-1;0!=(b&v);v>>>=1)b^=v;for(b^=v,k=(1<<A)-1;(b&k)!=r[w];)w--,A-=y,k=(1<<A)-1}return 0!==j&&1!=x?-5:0}function o(s){let o;for(e||(e=[],t=[],n=new Int32Array(16),i=[],a=new Int32Array(15),r=new Int32Array(16)),t.length<s&&(t=[]),o=0;o<s;o++)t[o]=0;for(o=0;o<16;o++)n[o]=0;for(o=0;o<3;o++)i[o]=0;a.set(n.subarray(0,15),0),r.set(n.subarray(0,16),0)}this.inflate_trees_bits=function(n,i,a,r,l){let d;return o(19),e[0]=0,d=s(n,0,19,19,null,null,a,i,r,e,t),-3==d?l.msg="oversubscribed dynamic bit lengths tree":-5!=d&&0!==i[0]||(l.msg="incomplete dynamic bit lengths tree",d=-3),d},this.inflate_trees_dynamic=function(n,i,a,r,l,d,c,f,u){let p;return o(288),e[0]=0,p=s(a,0,n,257,Y,J,d,r,f,e,t),0!=p||0===r[0]?(-3==p?u.msg="oversubscribed literal/length tree":-4!=p&&(u.msg="incomplete literal/length tree",p=-3),p):(o(288),p=s(a,n,i,0,Q,X,c,l,f,e,t),0!=p||0===l[0]&&n>257?(-3==p?u.msg="oversubscribed distance tree":-5==p?(u.msg="incomplete distance tree",p=-3):-4!=p&&(u.msg="empty distance tree with lengths",p=-3),p):0)}}function te(){const e=this;let t,n,i,a,r=0,s=0,o=0,l=0,d=0,c=0,f=0,u=0,p=0,m=0;function _(e,t,n,i,a,r,s,o){let l,d,c,f,u,p,m,_,h,x,w,b,v,g,y,k;m=o.next_in_index,_=o.avail_in,u=s.bitb,p=s.bitk,h=s.write,x=h<s.read?s.read-h-1:s.end-h,w=G[e],b=G[t];do{for(;p<20;)_--,u|=(255&o.read_byte(m++))<<p,p+=8;if(l=u&w,d=n,c=i,k=3*(c+l),0!==(f=d[k]))for(;;){if(u>>=d[k+1],p-=d[k+1],0!=(16&f)){for(f&=15,v=d[k+2]+(u&G[f]),u>>=f,p-=f;p<15;)_--,u|=(255&o.read_byte(m++))<<p,p+=8;for(l=u&b,d=a,c=r,k=3*(c+l),f=d[k];;){if(u>>=d[k+1],p-=d[k+1],0!=(16&f)){for(f&=15;p<f;)_--,u|=(255&o.read_byte(m++))<<p,p+=8;if(g=d[k+2]+(u&G[f]),u>>=f,p-=f,x-=v,h>=g)y=h-g,h-y>0&&2>h-y?(s.window[h++]=s.window[y++],s.window[h++]=s.window[y++],v-=2):(s.window.set(s.window.subarray(y,y+2),h),h+=2,y+=2,v-=2);else{y=h-g;do{y+=s.end}while(y<0);if(f=s.end-y,v>f){if(v-=f,h-y>0&&f>h-y)do{s.window[h++]=s.window[y++]}while(0!=--f);else s.window.set(s.window.subarray(y,y+f),h),h+=f,y+=f,f=0;y=0}}if(h-y>0&&v>h-y)do{s.window[h++]=s.window[y++]}while(0!=--v);else s.window.set(s.window.subarray(y,y+v),h),h+=v,y+=v,v=0;break}if(0!=(64&f))return o.msg="invalid distance code",v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,-3;l+=d[k+2],l+=u&G[f],k=3*(c+l),f=d[k]}break}if(0!=(64&f))return 0!=(32&f)?(v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,1):(o.msg="invalid literal/length code",v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,-3);if(l+=d[k+2],l+=u&G[f],k=3*(c+l),0===(f=d[k])){u>>=d[k+1],p-=d[k+1],s.window[h++]=d[k+2],x--;break}}else u>>=d[k+1],p-=d[k+1],s.window[h++]=d[k+2],x--}while(x>=258&&_>=10);return v=o.avail_in-_,v=p>>3<v?p>>3:v,_+=v,m-=v,p-=v<<3,s.bitb=u,s.bitk=p,o.avail_in=_,o.total_in+=m-o.next_in_index,o.next_in_index=m,s.write=h,0}e.init=function(e,r,s,o,l,d){t=0,f=e,u=r,i=s,p=o,a=l,m=d,n=null},e.proc=function(e,h,x){let w,b,v,g,y,k,z,U=0,A=0,S=0;for(S=h.next_in_index,g=h.avail_in,U=e.bitb,A=e.bitk,y=e.write,k=y<e.read?e.read-y-1:e.end-y;;)switch(t){case 0:if(k>=258&&g>=10&&(e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,x=_(f,u,i,p,a,m,e,h),S=h.next_in_index,g=h.avail_in,U=e.bitb,A=e.bitk,y=e.write,k=y<e.read?e.read-y-1:e.end-y,0!=x)){t=1==x?7:9;break}o=f,n=i,s=p,t=1;case 1:for(w=o;A<w;){if(0===g)return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);x=0,g--,U|=(255&h.read_byte(S++))<<A,A+=8}if(b=3*(s+(U&G[w])),U>>>=n[b+1],A-=n[b+1],v=n[b],0===v){l=n[b+2],t=6;break}if(0!=(16&v)){d=15&v,r=n[b+2],t=2;break}if(0==(64&v)){o=v,s=b/3+n[b+2];break}if(0!=(32&v)){t=7;break}return t=9,h.msg="invalid literal/length code",x=-3,e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);case 2:for(w=d;A<w;){if(0===g)return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);x=0,g--,U|=(255&h.read_byte(S++))<<A,A+=8}r+=U&G[w],U>>=w,A-=w,o=u,n=a,s=m,t=3;case 3:for(w=o;A<w;){if(0===g)return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);x=0,g--,U|=(255&h.read_byte(S++))<<A,A+=8}if(b=3*(s+(U&G[w])),U>>=n[b+1],A-=n[b+1],v=n[b],0!=(16&v)){d=15&v,c=n[b+2],t=4;break}if(0==(64&v)){o=v,s=b/3+n[b+2];break}return t=9,h.msg="invalid distance code",x=-3,e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);case 4:for(w=d;A<w;){if(0===g)return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);x=0,g--,U|=(255&h.read_byte(S++))<<A,A+=8}c+=U&G[w],U>>=w,A-=w,t=5;case 5:for(z=y-c;z<0;)z+=e.end;for(;0!==r;){if(0===k&&(y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k&&(e.write=y,x=e.inflate_flush(h,x),y=e.write,k=y<e.read?e.read-y-1:e.end-y,y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k)))return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);e.window[y++]=e.window[z++],k--,z==e.end&&(z=0),r--}t=0;break;case 6:if(0===k&&(y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k&&(e.write=y,x=e.inflate_flush(h,x),y=e.write,k=y<e.read?e.read-y-1:e.end-y,y==e.end&&0!==e.read&&(y=0,k=y<e.read?e.read-y-1:e.end-y),0===k)))return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);x=0,e.window[y++]=l,k--,t=0;break;case 7:if(A>7&&(A-=8,g++,S--),e.write=y,x=e.inflate_flush(h,x),y=e.write,k=y<e.read?e.read-y-1:e.end-y,e.read!=e.write)return e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);t=8;case 8:return x=1,e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);case 9:return x=-3,e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x);default:return x=-2,e.bitb=U,e.bitk=A,h.avail_in=g,h.total_in+=S-h.next_in_index,h.next_in_index=S,e.write=y,e.inflate_flush(h,x)}},e.free=function(){}}ee.inflate_trees_fixed=function(e,t,n,i){return e[0]=9,t[0]=5,n[0]=Z,i[0]=N,0};const ne=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function ie(e,t){const n=this;let i,a=0,r=0,s=0,o=0;const l=[0],d=[0],c=new te;let f=0,u=new Int32Array(4320);const p=new ee;n.bitk=0,n.bitb=0,n.window=new Uint8Array(t),n.end=t,n.read=0,n.write=0,n.reset=function(e,t){t&&(t[0]=0),6==a&&c.free(e),a=0,n.bitk=0,n.bitb=0,n.read=n.write=0},n.reset(e,null),n.inflate_flush=function(e,t){let i,a,r;return a=e.next_out_index,r=n.read,i=(r<=n.write?n.write:n.end)-r,i>e.avail_out&&(i=e.avail_out),0!==i&&-5==t&&(t=0),e.avail_out-=i,e.total_out+=i,e.next_out.set(n.window.subarray(r,r+i),a),a+=i,r+=i,r==n.end&&(r=0,n.write==n.end&&(n.write=0),i=n.write-r,i>e.avail_out&&(i=e.avail_out),0!==i&&-5==t&&(t=0),e.avail_out-=i,e.total_out+=i,e.next_out.set(n.window.subarray(r,r+i),a),a+=i,r+=i),e.next_out_index=a,n.read=r,t},n.proc=function(e,t){let m,_,h,x,w,b,v,g;for(x=e.next_in_index,w=e.avail_in,_=n.bitb,h=n.bitk,b=n.write,v=b<n.read?n.read-b-1:n.end-b;;){let y,k,z,U,A,S,j,E;switch(a){case 0:for(;h<3;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}switch(m=7&_,f=1&m,m>>>1){case 0:_>>>=3,h-=3,m=7&h,_>>>=m,h-=m,a=1;break;case 1:y=[],k=[],z=[[]],U=[[]],ee.inflate_trees_fixed(y,k,z,U),c.init(y[0],k[0],z[0],0,U[0],0),_>>>=3,h-=3,a=6;break;case 2:_>>>=3,h-=3,a=3;break;case 3:return _>>>=3,h-=3,a=9,e.msg="invalid block type",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t)}break;case 1:for(;h<32;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if((~_>>>16&65535)!=(65535&_))return a=9,e.msg="invalid stored block lengths",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);r=65535&_,_=h=0,a=0!==r?2:0!==f?7:0;break;case 2:if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);if(0===v&&(b==n.end&&0!==n.read&&(b=0,v=b<n.read?n.read-b-1:n.end-b),0===v&&(n.write=b,t=n.inflate_flush(e,t),b=n.write,v=b<n.read?n.read-b-1:n.end-b,b==n.end&&0!==n.read&&(b=0,v=b<n.read?n.read-b-1:n.end-b),0===v)))return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);if(t=0,m=r,m>w&&(m=w),m>v&&(m=v),n.window.set(e.read_buf(x,m),b),x+=m,w-=m,b+=m,v-=m,0!=(r-=m))break;a=0!==f?7:0;break;case 3:for(;h<14;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if(s=m=16383&_,(31&m)>29||(m>>5&31)>29)return a=9,e.msg="too many length or distance symbols",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);if(m=258+(31&m)+(m>>5&31),!i||i.length<m)i=[];else for(g=0;g<m;g++)i[g]=0;_>>>=14,h-=14,o=0,a=4;case 4:for(;o<4+(s>>>10);){for(;h<3;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}i[ne[o++]]=7&_,_>>>=3,h-=3}for(;o<19;)i[ne[o++]]=0;if(l[0]=7,m=p.inflate_trees_bits(i,l,d,u,e),0!=m)return-3==(t=m)&&(i=null,a=9),n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);o=0,a=5;case 5:for(;m=s,!(o>=258+(31&m)+(m>>5&31));){let r,c;for(m=l[0];h<m;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if(m=u[3*(d[0]+(_&G[m]))+1],c=u[3*(d[0]+(_&G[m]))+2],c<16)_>>>=m,h-=m,i[o++]=c;else{for(g=18==c?7:c-14,r=18==c?11:3;h<m+g;){if(0===w)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);t=0,w--,_|=(255&e.read_byte(x++))<<h,h+=8}if(_>>>=m,h-=m,r+=_&G[g],_>>>=g,h-=g,g=o,m=s,g+r>258+(31&m)+(m>>5&31)||16==c&&g<1)return i=null,a=9,e.msg="invalid bit length repeat",t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);c=16==c?i[g-1]:0;do{i[g++]=c}while(0!=--r);o=g}}if(d[0]=-1,A=[],S=[],j=[],E=[],A[0]=9,S[0]=6,m=s,m=p.inflate_trees_dynamic(257+(31&m),1+(m>>5&31),i,A,S,j,E,u,e),0!=m)return-3==m&&(i=null,a=9),t=m,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);c.init(A[0],S[0],u,j[0],u,E[0]),a=6;case 6:if(n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,1!=(t=c.proc(n,e,t)))return n.inflate_flush(e,t);if(t=0,c.free(e),x=e.next_in_index,w=e.avail_in,_=n.bitb,h=n.bitk,b=n.write,v=b<n.read?n.read-b-1:n.end-b,0===f){a=0;break}a=7;case 7:if(n.write=b,t=n.inflate_flush(e,t),b=n.write,v=b<n.read?n.read-b-1:n.end-b,n.read!=n.write)return n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);a=8;case 8:return t=1,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);case 9:return t=-3,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t);default:return t=-2,n.bitb=_,n.bitk=h,e.avail_in=w,e.total_in+=x-e.next_in_index,e.next_in_index=x,n.write=b,n.inflate_flush(e,t)}}},n.free=function(e){n.reset(e,null),n.window=null,u=null},n.set_dictionary=function(e,t,i){n.window.set(e.subarray(t,t+i),0),n.read=n.write=i},n.sync_point=function(){return 1==a?1:0}}const ae=[0,0,255,255];function re(){const e=this;function t(e){return e&&e.istate?(e.total_in=e.total_out=0,e.msg=null,e.istate.mode=7,e.istate.blocks.reset(e,null),0):-2}e.mode=0,e.method=0,e.was=[0],e.need=0,e.marker=0,e.wbits=0,e.inflateEnd=function(t){return e.blocks&&e.blocks.free(t),e.blocks=null,0},e.inflateInit=function(n,i){return n.msg=null,e.blocks=null,i<8||i>15?(e.inflateEnd(n),-2):(e.wbits=i,n.istate.blocks=new ie(n,1<<i),t(n),0)},e.inflate=function(e,t){let n,i;if(!e||!e.istate||!e.next_in)return-2;const a=e.istate;for(t=4==t?-5:0,n=-5;;)switch(a.mode){case 0:if(0===e.avail_in)return n;if(n=t,e.avail_in--,e.total_in++,8!=(15&(a.method=e.read_byte(e.next_in_index++)))){a.mode=13,e.msg="unknown compression method",a.marker=5;break}if(8+(a.method>>4)>a.wbits){a.mode=13,e.msg="invalid window size",a.marker=5;break}a.mode=1;case 1:if(0===e.avail_in)return n;if(n=t,e.avail_in--,e.total_in++,i=255&e.read_byte(e.next_in_index++),((a.method<<8)+i)%31!=0){a.mode=13,e.msg="incorrect header check",a.marker=5;break}if(0==(32&i)){a.mode=7;break}a.mode=2;case 2:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,a.need=(255&e.read_byte(e.next_in_index++))<<24&4278190080,a.mode=3;case 3:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,a.need+=(255&e.read_byte(e.next_in_index++))<<16&16711680,a.mode=4;case 4:if(0===e.avail_in)return n;n=t,e.avail_in--,e.total_in++,a.need+=(255&e.read_byte(e.next_in_index++))<<8&65280,a.mode=5;case 5:return 0===e.avail_in?n:(n=t,e.avail_in--,e.total_in++,a.need+=255&e.read_byte(e.next_in_index++),a.mode=6,2);case 6:return a.mode=13,e.msg="need dictionary",a.marker=0,-2;case 7:if(n=a.blocks.proc(e,n),-3==n){a.mode=13,a.marker=0;break}if(0==n&&(n=t),1!=n)return n;n=t,a.blocks.reset(e,a.was),a.mode=12;case 12:return 1;case 13:return-3;default:return-2}},e.inflateSetDictionary=function(e,t,n){let i=0,a=n;if(!e||!e.istate||6!=e.istate.mode)return-2;const r=e.istate;return a>=1<<r.wbits&&(a=(1<<r.wbits)-1,i=n-a),r.blocks.set_dictionary(t,i,a),r.mode=7,0},e.inflateSync=function(e){let n,i,a,r,s;if(!e||!e.istate)return-2;const o=e.istate;if(13!=o.mode&&(o.mode=13,o.marker=0),0===(n=e.avail_in))return-5;for(i=e.next_in_index,a=o.marker;0!==n&&a<4;)e.read_byte(i)==ae[a]?a++:a=0!==e.read_byte(i)?0:4-a,i++,n--;return e.total_in+=i-e.next_in_index,e.next_in_index=i,e.avail_in=n,o.marker=a,4!=a?-3:(r=e.total_in,s=e.total_out,t(e),e.total_in=r,e.total_out=s,o.mode=7,0)},e.inflateSyncPoint=function(e){return e&&e.istate&&e.istate.blocks?e.istate.blocks.sync_point():-2}}function se(){}function oe(e){const t=new se,n=e&&e.chunkSize?Math.floor(2*e.chunkSize):131072,i=new Uint8Array(n);let a=!1;t.inflateInit(),t.next_out=i,this.append=function(e,r){const s=[];let o,l,d=0,c=0,f=0;if(0!==e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,0!==t.avail_in||a||(t.next_in_index=0,a=!0),o=t.inflate(0),a&&-5===o){if(0!==t.avail_in)throw new Error("inflating: bad input")}else if(0!==o&&1!==o)throw new Error("inflating: "+t.msg);if((a||1===o)&&t.avail_in===e.length)throw new Error("inflating: bad input");t.next_out_index&&(t.next_out_index===n?s.push(new Uint8Array(i)):s.push(i.slice(0,t.next_out_index))),f+=t.next_out_index,r&&t.next_in_index>0&&t.next_in_index!=d&&(r(t.next_in_index),d=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return s.length>1?(l=new Uint8Array(f),s.forEach((function(e){l.set(e,c),c+=e.length}))):l=s[0]||new Uint8Array(0),l}},this.flush=function(){t.inflateEnd()}}se.prototype={inflateInit:function(e){const t=this;return t.istate=new re,e||(e=15),t.istate.inflateInit(t,e)},inflate:function(e){const t=this;return t.istate?t.istate.inflate(t,e):-2},inflateEnd:function(){const e=this;if(!e.istate)return-2;const t=e.istate.inflateEnd(e);return e.istate=null,t},inflateSync:function(){const e=this;return e.istate?e.istate.inflateSync(e):-2},inflateSetDictionary:function(e,t){const n=this;return n.istate?n.istate.inflateSetDictionary(n,e,t):-2},read_byte:function(e){return this.next_in[e]},read_buf:function(e,t){return this.next_in.subarray(e,e+t)}},self.initCodec=()=>{self.Deflate=K,self.Inflate=oe}}).toString(),t=URL.createObjectURL(new Blob(["("+e+")()"],{type:"text/javascript"}));O({workerScripts:{inflate:[t],deflate:[t]}})}})(),O({Deflate:function(e){const t=new k,n=e&&e.chunkSize?Math.floor(1.05*e.chunkSize):65536,i=new Uint8Array(n);let a=e?e.level:-1;void 0===a&&(a=-1),t.deflateInit(a),t.next_out=i,this.append=function(e,a){let r,s,o=0,l=0,d=0;const c=[];if(e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,r=t.deflate(0),0!=r)throw new Error("deflating: "+t.msg);t.next_out_index&&(t.next_out_index==n?c.push(new Uint8Array(i)):c.push(i.slice(0,t.next_out_index))),d+=t.next_out_index,a&&t.next_in_index>0&&t.next_in_index!=o&&(a(t.next_in_index),o=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return c.length>1?(s=new Uint8Array(d),c.forEach((function(e){s.set(e,l),l+=e.length}))):s=c[0]||new Uint8Array(0),s}},this.flush=function(){let e,a,r=0,s=0;const o=[];do{if(t.next_out_index=0,t.avail_out=n,e=t.deflate(4),1!=e&&0!=e)throw new Error("deflating: "+t.msg);n-t.avail_out>0&&o.push(i.slice(0,t.next_out_index)),s+=t.next_out_index}while(t.avail_in>0||0===t.avail_out);return t.deflateEnd(),a=new Uint8Array(s),o.forEach((function(e){a.set(e,r),r+=e.length})),a}},Inflate:function(e){const t=new M,n=e&&e.chunkSize?Math.floor(2*e.chunkSize):131072,i=new Uint8Array(n);let a=!1;t.inflateInit(),t.next_out=i,this.append=function(e,r){const s=[];let o,l,d=0,c=0,f=0;if(0!==e.length){t.next_in_index=0,t.next_in=e,t.avail_in=e.length;do{if(t.next_out_index=0,t.avail_out=n,0!==t.avail_in||a||(t.next_in_index=0,a=!0),o=t.inflate(0),a&&-5===o){if(0!==t.avail_in)throw new Error("inflating: bad input")}else if(0!==o&&1!==o)throw new Error("inflating: "+t.msg);if((a||1===o)&&t.avail_in===e.length)throw new Error("inflating: bad input");t.next_out_index&&(t.next_out_index===n?s.push(new Uint8Array(i)):s.push(i.slice(0,t.next_out_index))),f+=t.next_out_index,r&&t.next_in_index>0&&t.next_in_index!=d&&(r(t.next_in_index),d=t.next_in_index)}while(t.avail_in>0||0===t.avail_out);return s.length>1?(l=new Uint8Array(f),s.forEach((function(e){l.set(e,c),c+=e.length}))):l=s[0]||new Uint8Array(0),l}},this.flush=function(){t.inflateEnd()}}});const tt=["boot","dt","dtbo","init_boot","pvmfw","recovery","vbmeta_system","vbmeta_vendor","vbmeta","vendor_boot","vendor_kernel_boot"],nt=["odm","odm_dlkm","product","system_dlkm","system_ext","system","vendor_dlkm","vendor"];async function it(e,t,n){try{return await e.getData(t,n)}catch(e){throw e instanceof ProgressEvent&&"error"===e.type&&null!==e.target?e.target.error:e}}async function at(e,t,i,a){n(`Unpacking ${a}`),i("unpack",a,0);let r=await it(t,new G("application/octet-stream"),{onprogress:(e,t)=>{i("unpack",a,e/t)}});n(`Flashing ${a}`),i("flash",a,0),await e.flashBlob(a,r,(e=>{i("flash",a,e)}))}async function rt(e,t,n,i){for(let a of i){let i=new RegExp(`${a}(?:-.+)?\\.img$`),r=t.find((e=>e.filename.match(i)));void 0!==r&&await at(e,r,n,a)}}async function st(e,t,n){try{await e.reboot(t,!1)}catch(e){}await e.waitForConnect(n)}async function ot(e,t,i,s,o=((e,t,n)=>{})){o("load","package",0);let l=new Ve(new K(t)),d=await l.getEntries();"yes"===await e.getVariable("is-userspace")&&await e.reboot("bootloader",!0,s),await rt(e,d,o,["bootloader"]),await r(o,"reboot","device",4e3,st(e,"bootloader",s)),await rt(e,d,o,["radio"]),await r(o,"reboot","device",4e3,st(e,"bootloader",s));let c=await e.getVariable("snapshot-update-status");null!==c&&"none"!==c&&await e.runCommand("snapshot-update:cancel"),n("Loading nested images from zip"),o("unpack","images",0);let f=d.find((e=>e.filename.match(/image-.+\.zip$/))),u=await it(f,new G("application/zip"),{onprogress:(e,t)=>{o("unpack","images",e/t)}}),p=new Ve(new K(u)),m=await p.getEntries();if(f=m.find((e=>"android-info.txt"===e.filename)),void 0!==f){let t=await it(f,new H);await async function(e,t){for(let i of t.replace("\r","").split("\n")){let t=i.match(/^require\s+(.+?)=(.+)$/);if(!t)continue;let a=t[1];"board"===a&&(a="product");let r=t[2],s=r.split("|");if("partition-exists"===a){let t=await e.getVariable(`has-slot:${r}`);if("yes"!==t&&"no"!==t)throw new dt("FAIL",`Requirement ${a}=${r} failed, device lacks partition`);if(!tt.includes(r)&&!nt.includes(r))throw new dt("FAIL",`Requirement ${a}=${r} failed, unrecognized partition`)}else{let t=await e.getVariable(a);if(!s.includes(t)){let e=`Requirement ${a}=${r} failed, value = ${t}`;throw n(e),new dt("FAIL",e)}n(`Requirement ${a}=${r} passed`)}}}(e,t)}if(await rt(e,m,o,tt),f=m.find((e=>"super_empty.img"===e.filename)),void 0!==f){await r(o,"reboot","device",16e3,e.reboot("fastboot",!0,s));let t=await e.getVariable("super-partition-name");t||(t="super");let n=i?"wipe":"flash";o(n,"super",0);let l=await it(f,new G("application/octet-stream"));await e.upload(t,await a(l),(e=>{o(n,"super",e)})),await e.runCommand(`update-super:${t}${i?":wipe":""}`)}await rt(e,m,o,nt),"yes"===await e.getVariable("is-userspace")&&await r(o,"reboot","device",4e3,e.reboot("bootloader",!0,s)),f=d.find((e=>e.filename.endsWith("avb_pkmd.bin"))),void 0!==f&&(await e.runCommand("erase:avb_custom_key"),await at(e,f,o,"avb_custom_key")),i&&await r(o,"wipe","data",1e3,e.runCommand("erase:userdata"))}class lt extends Error{constructor(e){super(e),this.name="UsbError"}}class dt extends Error{constructor(e,t){super(`Bootloader replied with ${e}: ${t}`),this.status=e,this.bootloaderMessage=t,this.name="FastbootError"}}exports.FastbootDevice=class{constructor(){this.device=null,this.epIn=null,this.epOut=null,this._registeredUsbListeners=!1,this._connectResolve=null,this._connectReject=null,this._disconnectResolve=null}get isConnected(){return null!==this.device&&this.device.opened&&this.device.configurations[0].interfaces[0].claimed}async _validateAndConnectDevice(){if(null===this.device)throw new lt("Attempted to connect to null device");let e=this.device.configurations[0].interfaces[0].alternates[0];if(2!==e.endpoints.length)throw new lt("Interface has wrong number of endpoints");this.epIn=null,this.epOut=null;for(let t of e.endpoints){if(i("Checking endpoint:",t),"bulk"!==t.type)throw new lt("Interface endpoint is not bulk");if("in"===t.direction){if(null!==this.epIn)throw new lt("Interface has multiple IN endpoints");this.epIn=t.endpointNumber}else if("out"===t.direction){if(null!==this.epOut)throw new lt("Interface has multiple OUT endpoints");this.epOut=t.endpointNumber}}i("Endpoints: in =",this.epIn,", out =",this.epOut);try{await this.device.open();try{await this.device.reset()}catch(e){}await this.device.selectConfiguration(1),await this.device.claimInterface(0)}catch(e){throw null!==this._connectReject&&(this._connectReject(e),this._connectResolve=null,this._connectReject=null),e}null!==this._connectResolve&&(this._connectResolve(void 0),this._connectResolve=null,this._connectReject=null)}async waitForDisconnect(){if(null!==this.device)return await new Promise(((e,t)=>{this._disconnectResolve=e}))}async waitForConnect(e=(()=>{})){return navigator.userAgent.includes("Android")&&(await this.waitForDisconnect(),e()),await new Promise(((e,t)=>{this._connectResolve=e,this._connectReject=t}))}async connect(){let e=await navigator.usb.getDevices();n("Found paired USB devices:",e),1===e.length?this.device=e[0]:(n("No or multiple paired devices are connected, requesting one"),this.device=await navigator.usb.requestDevice({filters:[{classCode:255,subclassCode:66,protocolCode:3}]})),n("Using USB device:",this.device),this._registeredUsbListeners||(navigator.usb.addEventListener("disconnect",(e=>{e.device===this.device&&(n("USB device disconnected"),null!==this._disconnectResolve&&(this._disconnectResolve(void 0),this._disconnectResolve=null))})),navigator.usb.addEventListener("connect",(async e=>{n("USB device connected"),this.device=e.device;let t=null!==this._connectReject;try{await this._validateAndConnectDevice()}catch(e){if(!t)throw e}})),this._registeredUsbListeners=!0),await this._validateAndConnectDevice()}async _readResponse(){let e,t={text:""};do{let i=await this.device.transferIn(this.epIn,64),a=(new TextDecoder).decode(i.data);e=a.substring(0,4);let r=a.substring(4);if(n(`Response: ${e} ${r}`),"OKAY"===e)t.text+=r;else if("INFO"===e)t.text+=r+"\n";else{if("DATA"!==e)throw new dt(e,r);t.dataSize=r}}while("INFO"===e);return t}async runCommand(e){if(e.length>64)throw new RangeError;let t=(new TextEncoder).encode(e);return await this.device.transferOut(this.epOut,t),n("Command:",e),this._readResponse()}async getVariable(e){let t;try{t=(await(n=this.runCommand(`getvar:${e}`),i=1e4,new Promise(((e,t)=>{let a=!1,r=setTimeout((()=>{a=!0,t(new s(i))}),i);n.then((t=>{a||e(t)})).catch((e=>{a||t(e)})).finally((()=>{a||clearTimeout(r)}))})))).text}catch(e){if(!(e instanceof dt&&"FAIL"==e.status))throw e;t=null}var n,i;return t?t.trim():null}async _getDownloadSize(){try{let e=(await this.getVariable("max-download-size")).toLowerCase();if(e)return Math.min(parseInt(e,16),1073741824)}catch(e){}return 536870912}async _sendRawPayload(e,t){let n=0,a=e.byteLength;for(;a>0;){let r=e.slice(16384*n,16384*(n+1));n%1e3==0&&i(`  Sending ${r.byteLength} bytes to endpoint, ${a} remaining, i=${n}`),n%10==0&&t((e.byteLength-a)/e.byteLength),await this.device.transferOut(this.epOut,r),a-=r.byteLength,n+=1}t(1)}async upload(e,t,i=(e=>{})){n(`Uploading single sparse to ${e}: ${t.byteLength} bytes`);let a=t.byteLength.toString(16).padStart(8,"0");if(8!==a.length)throw new dt("FAIL",`Transfer size overflow: ${a} is more than 8 digits`);let r=await this.runCommand(`download:${a}`);if(void 0===r.dataSize)throw new dt("FAIL",`Unexpected response to download command: ${r.text}`);if(parseInt(r.dataSize,16)!==t.byteLength)throw new dt("FAIL",`Bootloader wants ${t.byteLength} bytes, requested to send ${t.byteLength} bytes`);n(`Sending payload: ${t.byteLength} bytes`),await this._sendRawPayload(t,i),n("Payload sent, waiting for response..."),await this._readResponse()}async reboot(e="",t=!1,n=(()=>{})){e.length>0?await this.runCommand(`reboot-${e}`):await this.runCommand("reboot"),t&&await this.waitForConnect(n)}async flashBlob(e,t,r=(e=>{})){"yes"===await this.getVariable(`has-slot:${e}`)&&(e+="_"+await this.getVariable("current-slot"));let s=await this._getDownloadSize(),d=await a(t.slice(0,28)),m=t.size,_=!1;try{let e=c(d);null!==e&&(m=e.blocks*e.blockSize,_=!0)}catch(e){}"yes"===await this.getVariable(`is-logical:${e}`)&&(await this.runCommand(`resize-logical-partition:${e}:0`),await this.runCommand(`resize-logical-partition:${e}:${m}`)),t.size>s&&!_&&(n(`${e} image is raw, converting to sparse`),t=await async function(e){let t={blockSize:4096,blocks:e.size/4096,chunks:1,crc32:0},n=[];for(;e.size>0;){let i=Math.min(e.size,67108864);n.push({type:l.Raw,blocks:i/t.blockSize,data:e.slice(0,i)}),e=e.slice(i)}return p(t,n)}(t)),n(`Flashing ${t.size} bytes to ${e}, ${s} bytes per split`);let h=0,x=0;for await(let d of async function*(e,t){if(n(`Splitting ${e.size}-byte sparse image into ${t}-byte chunks`),e.size<=t)return n("Blob fits in 1 payload, not splitting"),void(yield{data:await a(e),bytes:e.size});let r=c(await a(e.slice(0,28)));if(null===r)throw new o("Blob is not a sparse image");r.crc32=0,e=e.slice(28);let s=[],d=0;for(let o=0;o<r.chunks;o++){let c=f(await a(e.slice(0,12)));c.data=e.slice(12,12+c.dataBytes),e=e.slice(12+c.dataBytes);let _=t-(28+12*(m=s).length+function(e){return e.map((e=>e.data.size)).reduce(((e,t)=>e+t),0)}(m));if(i(`  Chunk ${o}: type ${c.type}, ${c.dataBytes} bytes / ${c.blocks} blocks, ${_} bytes remaining`),_>=c.dataBytes)i("    Space is available, adding chunk"),s.push(c),d+=c.blocks*r.blockSize;else{let e=u(s);s.push({type:l.Skip,blocks:r.blocks-e,data:new Blob([]),dataBytes:0}),i(`Partition is ${r.blocks} blocks, used ${e}, padded with ${r.blocks-e}, finishing split with ${u(s)} blocks`);let t=await p(r,s);n(`Finished ${t.size}-byte split with ${s.length} chunks`),yield{data:await a(t),bytes:d},i(`Starting new split: skipping first ${e} blocks and adding chunk`),s=[{type:l.Skip,blocks:e,data:new Blob([]),dataBytes:0},c],d=0}}var m;if(s.length>0&&(s.length>1||s[0].type!==l.Skip)){let e=await p(r,s);n(`Finishing final ${e.size}-byte split with ${s.length} chunks`),yield{data:await a(e),bytes:d}}}(t,s))await this.upload(e,d.data,(e=>{r((x+e*d.bytes)/m)})),n("Flashing payload..."),await this.runCommand(`flash:${e}`),h+=1,x+=d.bytes;n(`Flashed ${e} with ${h} split(s)`)}async bootBlob(e,t=(e=>{})){n(`Booting ${e.size} bytes image`);let i=await a(e);await this.upload("boot.img",i,t),n("Booting payload..."),await this.runCommand("boot"),n(`Booted ${e.size} bytes image`)}async flashFactoryZip(e,t,n,i=(e=>{})){return await ot(this,e,t,n,i)}},exports.FastbootError=dt,exports.TimeoutError=s,exports.USER_ACTION_MAP={load:"Loading",unpack:"Unpacking",flash:"Writing",wipe:"Wiping",reboot:"Restarting"},exports.UsbError=lt,exports.configureZip=O,exports.setDebugLevel=function(e){t=e};
//# sourceMappingURL=fastboot.min.cjs.map
