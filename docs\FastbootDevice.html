<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> FastbootDevice</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    API Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Classes</h3><ul><li><a href="FastbootDevice.html">FastbootDevice</a></li><li><a href="FastbootError.html">FastbootError</a></li><li><a href="TimeoutError.html">TimeoutError</a></li><li><a href="UsbError.html">UsbError</a></li></ul><h3>Global</h3><ul><li><a href="global.html#setDebugLevel">setDebugLevel</a></li><li><a href="global.html#USER_ACTION_MAP">USER_ACTION_MAP</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Class</p>
                    <h1>FastbootDevice</h1>
                </header>
                




<section>

<header>
    
        <h2><span class="attribs"><span class="type-signature"></span></span>FastbootDevice<span class="signature">()</span><span class="type-signature"></span></h2>
        
            <div class="class-description">This class is a client for executing fastboot commands and operations on a
device connected over USB.</div>
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class='vertical-section'>
                <div class="members">
                    <div class="member">
                        <div class=name>
                            <span class="tag">Constructor</span>
                        </div>
                        


    
    <h4 class="name" id="FastbootDevice">
        <a class="href-link" href="#FastbootDevice">#</a>
        
        <span class="code-name">
            
                new FastbootDevice<span class="signature">()</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Create a new fastboot device instance. This doesn't actually connect to
any USB devices; call connect to do so.
    </div>
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line45">line 45</a>
            </span>
        </p>
    
</dl>






















                    </div>
                </div>
            </div>
        
    
    </div>
    
    

    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="FastbootDevice.html">FastbootDevice</a></dt>
            <dd></dd>
        </dl>
    

    

    

    

    
        <div class='vertical-section'>
            <h1>Members</h1>
            <div class="members">
            
                <div class="member">

<h4 class="name" id="isConnected">
    <a class="href-link" href="#isConnected">#</a>
    
    <span class="code-name">
        isConnected
    </span>
    
</h4>




<div class="description">
    Returns whether a USB device is connected and ready for use.
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line61">line 61</a>
            </span>
        </p>
    
</dl>





</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Methods</h1>
            <div class="members">
            
                <div class="member">


    
    <h4 class="name" id="connect">
        <a class="href-link" href="#connect">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                connect<span class="signature">()</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Request the user to select a USB device and connect to it using the
fastboot protocol.
    </div>
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line186">line 186</a>
            </span>
        </p>
    
</dl>
















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Throws:</label></div>
        <div class="column is-10">
            
                    
<div class="columns">
    
        
            <div class='column is-12 has-text-left'>
            <label>Type: </label>
<span class="param-type"><a href="UsbError.html">UsbError</a></span>


            </div>
        
    
</div>

                
        </div>
    </div>






</div>
            
                <div class="member">


    
    <h4 class="name" id="flashBlob">
        <a class="href-link" href="#flashBlob">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                flashBlob<span class="signature">(partition, blob, onProgress)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Flash the given Blob to the given partition on the device. Any image
format supported by the bootloader is allowed, e.g. sparse or raw images.
Large raw images will be converted to sparse images automatically, and
large sparse images will be split and flashed in multiple passes
depending on the bootloader's payload size limit.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>partition</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  

  <td class="description last">The name of the partition to flash.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>blob</code></td>
  

  <td class="type">
  
      
<span class="param-type">Blob</span>


  
  </td>

  

  

  <td class="description last">The Blob to retrieve data from.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>onProgress</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="global.html#ProgressCallback">ProgressCallback</a></span>


  
  </td>

  

  

  <td class="description last">Callback for flashing progress updates.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line481">line 481</a>
            </span>
        </p>
    
</dl>
















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Throws:</label></div>
        <div class="column is-10">
            
                    
<div class="columns">
    
        
            <div class='column is-12 has-text-left'>
            <label>Type: </label>
<span class="param-type"><a href="FastbootError.html">FastbootError</a></span>


            </div>
        
    
</div>

                
        </div>
    </div>






</div>
            
                <div class="member">


    
    <h4 class="name" id="flashFactoryZip">
        <a class="href-link" href="#flashFactoryZip">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                flashFactoryZip<span class="signature">(device, blob, wipe, onReconnect, onProgress)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Flash the given factory images zip onto the device, with automatic handling
of firmware, system, and logical partitions as AOSP fastboot and
flash-all.sh would do.
Equivalent to `fastboot update name.zip`.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>device</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="FastbootDevice.html">FastbootDevice</a></span>


  
  </td>

  

  

  <td class="description last">Fastboot device to flash.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>blob</code></td>
  

  <td class="type">
  
      
<span class="param-type">Blob</span>


  
  </td>

  

  

  <td class="description last">Blob containing the zip file to flash.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>wipe</code></td>
  

  <td class="type">
  
      
<span class="param-type">boolean</span>


  
  </td>

  

  

  <td class="description last">Whether to wipe super and userdata. Equivalent to `fastboot -w`.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>onReconnect</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="global.html#ReconnectCallback">ReconnectCallback</a></span>


  
  </td>

  

  

  <td class="description last">Callback to request device reconnection.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>onProgress</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="global.html#FactoryFlashCallback">FactoryFlashCallback</a></span>


  
  </td>

  

  

  <td class="description last">Progress callback for image flashing.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line572">line 572</a>
            </span>
        </p>
    
</dl>





















</div>
            
                <div class="member">


    
    <h4 class="name" id="getVariable">
        <a class="href-link" href="#getVariable">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                getVariable<span class="signature">(varName)</span><span class="type-signature"> &rarr; {value}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Read the value of a bootloader variable. Returns undefined if the variable
does not exist.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>varName</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  

  <td class="description last">The name of the variable to get.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line314">line 314</a>
            </span>
        </p>
    
</dl>
















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Throws:</label></div>
        <div class="column is-10">
            
                    
<div class="columns">
    
        
            <div class='column is-12 has-text-left'>
            <label>Type: </label>
<span class="param-type"><a href="FastbootError.html">FastbootError</a></span>


            </div>
        
    
</div>

                
        </div>
    </div>



    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'>Textual content of the variable.</div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<span class="param-type">value</span>


    </div>
    
</div>

                
        </div>
    </div>




</div>
            
                <div class="member">


    
    <h4 class="name" id="reboot">
        <a class="href-link" href="#reboot">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                reboot<span class="signature">(target, wait, onReconnect)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Reboot to the given target, and optionally wait for the device to
reconnect.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            
            <th>Default</th>
            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>target</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  
      <td class="default">
      
      </td>
  

  <td class="description last">Where to reboot to, i.e. fastboot or bootloader.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>wait</code></td>
  

  <td class="type">
  
      
<span class="param-type">boolean</span>


  
  </td>

  

  
      <td class="default">
      
          false
      
      </td>
  

  <td class="description last">Whether to wait for the device to reconnect.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>onReconnect</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="global.html#ReconnectCallback">ReconnectCallback</a></span>


  
  </td>

  

  
      <td class="default">
      
      </td>
  

  <td class="description last">Callback to request device reconnection, if wait is enabled.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line457">line 457</a>
            </span>
        </p>
    
</dl>





















</div>
            
                <div class="member">


    
    <h4 class="name" id="runCommand">
        <a class="href-link" href="#runCommand">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                runCommand<span class="signature">(command)</span><span class="type-signature"> &rarr; {response}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Send a textual command to the bootloader.
This is in raw fastboot format, not AOSP fastboot syntax.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>command</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  

  <td class="description last">The command to send.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line292">line 292</a>
            </span>
        </p>
    
</dl>
















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Throws:</label></div>
        <div class="column is-10">
            
                    
<div class="columns">
    
        
            <div class='column is-12 has-text-left'>
            <label>Type: </label>
<span class="param-type"><a href="FastbootError.html">FastbootError</a></span>


            </div>
        
    
</div>

                
        </div>
    </div>



    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'>Object containing response text and data size, if any.</div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<span class="param-type">response</span>


    </div>
    
</div>

                
        </div>
    </div>




</div>
            
                <div class="member">


    
    <h4 class="name" id="upload">
        <a class="href-link" href="#upload">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                upload<span class="signature">(partition, buffer, onProgress)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Upload a payload to the bootloader for further use, e.g. for flashing.
Does not handle raw images, flashing, or splitting.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>partition</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  

  <td class="description last">Name of the partition the payload is intended for.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>buffer</code></td>
  

  <td class="type">
  
      
<span class="param-type">ArrayBuffer</span>


  
  </td>

  

  

  <td class="description last">Buffer containing the data to upload.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>onProgress</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="global.html#ProgressCallback">ProgressCallback</a></span>


  
  </td>

  

  

  <td class="description last">Callback for upload progress updates.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line412">line 412</a>
            </span>
        </p>
    
</dl>
















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Throws:</label></div>
        <div class="column is-10">
            
                    
<div class="columns">
    
        
            <div class='column is-12 has-text-left'>
            <label>Type: </label>
<span class="param-type"><a href="FastbootError.html">FastbootError</a></span>


            </div>
        
    
</div>

                
        </div>
    </div>






</div>
            
                <div class="member">


    
    <h4 class="name" id="waitForConnect">
        <a class="href-link" href="#waitForConnect">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                waitForConnect<span class="signature">(onReconnect)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Wait for the USB device to connect. Returns at the next connection,
regardless of whether the connected USB device matches the previous one.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>onReconnect</code></td>
  

  <td class="type">
  
      
<span class="param-type"><a href="global.html#ReconnectCallback">ReconnectCallback</a></span>


  
  </td>

  

  

  <td class="description last">Callback to request device reconnection on Android.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line166">line 166</a>
            </span>
        </p>
    
</dl>





















</div>
            
                <div class="member">


    
    <h4 class="name" id="waitForDisconnect">
        <a class="href-link" href="#waitForDisconnect">#</a>
        
            
                <span class='tag'>async</span>
            
        
        <span class="code-name">
            
                waitForDisconnect<span class="signature">()</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Wait for the current USB device to disconnect, if it's still connected.
Returns immediately if no device is connected.
    </div>
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line141">line 141</a>
            </span>
        </p>
    
</dl>





















</div>
            
            </div>
        </div>
    

    

    
</article>

</section>




            </div>
            
            <footer class="footer">
                <div class="content has-text-centered">
                    <p>Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a></p>
                    <p class="sidebar-created-by">
                        <a href="https://github.com/SoftwareBrothers/better-docs" target="_blank">BetterDocs theme</a> provided with <i class="fas fa-heart"></i> by 
                        <a href="http://softwarebrothers.co" target="_blank">SoftwareBrothers - JavaScript Development Agency</a>
                    </p>
                </div>
            </footer>
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

</body>
</html>