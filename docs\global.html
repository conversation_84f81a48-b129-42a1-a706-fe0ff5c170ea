<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title> Global</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    API Documentation
                </a>
                
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Classes</h3><ul><li><a href="FastbootDevice.html">FastbootDevice</a></li><li><a href="FastbootError.html">FastbootError</a></li><li><a href="TimeoutError.html">TimeoutError</a></li><li><a href="UsbError.html">UsbError</a></li></ul><h3>Global</h3><ul><li><a href="global.html#setDebugLevel">setDebugLevel</a></li><li><a href="global.html#USER_ACTION_MAP">USER_ACTION_MAP</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Title</p>
                    <h1>Global</h1>
                </header>
                




<section>

<header>
    
        <h2></h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

        


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
</dl>


        
    
    </div>
    
    

    

    

    

    

    

    
        <div class='vertical-section'>
            <h1>Members</h1>
            <div class="members">
            
                <div class="member">

<h4 class="name" id="USER_ACTION_MAP">
    <a class="href-link" href="#USER_ACTION_MAP">#</a>
    
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        USER_ACTION_MAP
    </span>
    
</h4>




<div class="description">
    User-friendly action strings for factory image flashing progress.
This can be indexed by the action argument in FactoryFlashCallback.
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="factory.js.html" class="button">View Source</a>
            <span>
                <a href="factory.js.html">factory.js</a>, <a href="factory.js.html#line22">line 22</a>
            </span>
        </p>
    
</dl>





</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Methods</h1>
            <div class="members">
            
                <div class="member">


    
    <h4 class="name" id="setDebugLevel">
        <a class="href-link" href="#setDebugLevel">#</a>
        
        <span class="code-name">
            
                setDebugLevel<span class="signature">(level)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Change the debug level for the fastboot client:
  - 0 = silent
  - 1 = debug, recommended for general use
  - 2 = verbose, for debugging only
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>level</code></td>
  

  <td class="type">
  
      
<span class="param-type">number</span>


  
  </td>

  

  

  <td class="description last">Debug level to use.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="common.js.html" class="button">View Source</a>
            <span>
                <a href="common.js.html">common.js</a>, <a href="common.js.html#line23">line 23</a>
            </span>
        </p>
    
</dl>





















</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Type Definitions</h1>
            <div class="members">
            
                <div class="member">


    
    <h4 class="name" id="FactoryFlashCallback">
        <a class="href-link" href="#FactoryFlashCallback">#</a>
        
        <span class="code-name">
            
                FactoryFlashCallback<span class="signature">(action, item, progress)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Callback for factory image flashing progress.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>action</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  

  <td class="description last">Action in the flashing process, e.g. unpack/flash.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>item</code></td>
  

  <td class="type">
  
      
<span class="param-type">string</span>


  
  </td>

  

  

  <td class="description last">Item processed by the action, e.g. partition being flashed.</td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>progress</code></td>
  

  <td class="type">
  
      
<span class="param-type">number</span>


  
  </td>

  

  

  <td class="description last">Progress within the current action between 0 and 1.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line551">line 551</a>
            </span>
        </p>
    
</dl>





















</div>
            
                <div class="member">


    
    <h4 class="name" id="ProgressCallback">
        <a class="href-link" href="#ProgressCallback">#</a>
        
        <span class="code-name">
            
                ProgressCallback<span class="signature">(progress)</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Callback for progress updates while flashing or uploading an image.
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>progress</code></td>
  

  <td class="type">
  
      
<span class="param-type">number</span>


  
  </td>

  

  

  <td class="description last">Progress for the current action, between 0 and 1.</td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line363">line 363</a>
            </span>
        </p>
    
</dl>





















</div>
            
                <div class="member">


    
    <h4 class="name" id="ReconnectCallback">
        <a class="href-link" href="#ReconnectCallback">#</a>
        
        <span class="code-name">
            
                ReconnectCallback<span class="signature">()</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Callback for reconnecting to the USB device.
This is necessary because some platforms do not support automatic reconnection,
and USB connection requests can only be triggered as the result of explicit
user action.
    </div>
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line151">line 151</a>
            </span>
        </p>
    
</dl>





















</div>
            
                <div class="member">


    
    <h4 class="name" id="ReconnectCallback">
        <a class="href-link" href="#ReconnectCallback">#</a>
        
        <span class="code-name">
            
                ReconnectCallback<span class="signature">()</span><span class="type-signature"></span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        Callback for reconnecting the USB device.
This is necessary because some platforms do not support automatic reconnection,
and USB connection requests can only be triggered as the result of explicit
user action.
    </div>
    













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="fastboot.js.html" class="button">View Source</a>
            <span>
                <a href="fastboot.js.html">fastboot.js</a>, <a href="fastboot.js.html#line542">line 542</a>
            </span>
        </p>
    
</dl>





















</div>
            
            </div>
        </div>
    

    
</article>

</section>




            </div>
            
            <footer class="footer">
                <div class="content has-text-centered">
                    <p>Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a></p>
                    <p class="sidebar-created-by">
                        <a href="https://github.com/SoftwareBrothers/better-docs" target="_blank">BetterDocs theme</a> provided with <i class="fas fa-heart"></i> by 
                        <a href="http://softwarebrothers.co" target="_blank">SoftwareBrothers - JavaScript Development Agency</a>
                    </p>
                </div>
            </footer>
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

</body>
</html>